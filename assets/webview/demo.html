<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebView JS通信演示</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            margin: 0;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
        }
        .card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
            backdrop-filter: blur(10px);
        }
        button {
            background: #007AFF;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            margin: 5px;
            transition: background 0.3s;
        }
        button:hover {
            background: #0056cc;
        }
        .log {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 5px;
            padding: 10px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 200px;
            overflow-y: auto;
        }
        input {
            width: 100%;
            padding: 10px;
            border: none;
            border-radius: 5px;
            margin: 5px 0;
            box-sizing: border-box;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <h1>🚀 WebView JS通信演示</h1>
            <p>这是一个演示Flutter WebView与JavaScript通信的页面</p>
        </div>

        <div class="card">
            <h3>📱 Flutter通信测试</h3>
            <button onclick="sendMessageToFlutter('hello')">发送Hello消息</button>
            <button onclick="sendMessageToFlutter('getData')">请求数据</button>
            <button onclick="getDeviceInfo()">获取设备信息</button>
            <button onclick="changeTitle()">更改页面标题</button>
        </div>

        <div class="card">
            <h3>📋 自定义消息</h3>
            <input type="text" id="customMessage" placeholder="输入自定义消息..." />
            <button onclick="sendCustomMessage()">发送自定义消息</button>
        </div>

        <div class="card">
            <h3>📊 消息日志</h3>
            <div id="log" class="log">等待消息...</div>
            <button onclick="clearLog()">清空日志</button>
        </div>

        <div class="card">
            <h3>🎮 页面操作</h3>
            <button onclick="openUrl()">打开浏览器</button>
            <button onclick="showToast('这是来自WebView的提示!')">显示提示</button>
            <button onclick="navigateToNewPage()">打开新页面</button>
            <button onclick="closePage()">关闭页面</button>
            <button onclick="isHasPermission()">是否有通知权限</button>
            <button onclick="requestPermission()">申请通知权限</button>
        </div>
    </div>

    <script>
        let messageCount = 0;

        function log(message) {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logElement.innerHTML += `[${timestamp}] ${message}<br>`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '日志已清空<br>';
        }

        function sendMessageToFlutter(type) {
            messageCount++;
            const message = {
                type: 'userAction',
                method: type,
                data: {
                    message: `来自WebView的消息 #${messageCount}`,
                    timestamp: Date.now(),
                    userAgent: navigator.userAgent
                },
                id: `msg_${messageCount}`
            };

            if (window.FlutterJSBridge) {
                window.FlutterJSBridge.postMessage(message);
                log(`发送消息: ${type}`);
            } else {
                log('错误: FlutterJSBridge 未找到');
            }
        }

        function sendCustomMessage() {
            const input = document.getElementById('customMessage');
            const message = input.value.trim();
            if (!message) {
                alert('请输入消息内容');
                return;
            }

            if (window.FlutterJSBridge) {
                window.FlutterJSBridge.postMessage({
                    type: 'custom',
                    method: 'customMessage',
                    data: { message: message },
                    id: `custom_${Date.now()}`
                });
                log(`发送自定义消息: ${message}`);
                input.value = '';
            } else {
                log('错误: FlutterJSBridge 未找到');
            }
        }

        function getDeviceInfo() {
            log("jho web调用了getDeviceInfo");
            if (window.FlutterJSBridge) {
                window.FlutterJSBridge.postMessage(JSON.stringify({
                    method: 'getDeviceInfo',
                    data: {  }
                }));
                log('请求设备信息...');
            } else {
                log('错误: FlutterJSBridge 未找到');
            }
        }

        function changeTitle() {
            const newTitle = `新标题 ${Date.now()}`;
            if (window.FlutterJSBridge) {
                window.FlutterJSBridge.postMessage(JSON.stringify({
                    method: 'setTitle',
                    data: {
                        "title": "newTitle"
                    }
                }));
                log(`更改标题: ${newTitle}`);
            }
        }

        function showToast(message) {
            if (window.FlutterJSBridge) {
                window.FlutterJSBridge.postMessage(JSON.stringify({
                    method: 'showToast',
                    data: { 
                        "message": "123456",
                        "duration": 2000
                    }
                }));
                log(`显示提示: ${message}`);
            }
        }

        function navigateToNewPage() {
            if (window.FlutterJSBridge) {
                window.FlutterJSBridge.postMessage(JSON.stringify({
                    method: 'navigate',
                    data: { url: 'https://flutter.dev' }
                }));
                log('请求导航到新页面');
            }
        }

        function closePage() {
            if (window.FlutterJSBridge) {
                window.FlutterJSBridge.postMessage(JSON.stringify({
                    method: 'close',
                    data: { }
                }));
                log('请求关闭页面');
            }
        }

        function isHasPermission() {
            if (window.FlutterJSBridge) {
                window.FlutterJSBridge.postMessage(JSON.stringify({
                    method: 'isHasPermission',
                    data: {
                        "permissionName": "notification",
                        "callback": "callback"
                    }
                }));
                log('查询权限');
            }
        }

        function requestPermission() {
            if (window.FlutterJSBridge) {
                window.FlutterJSBridge.postMessage(JSON.stringify({
                    method: 'requestPermission',
                    data: {
                        "permissionName": "notification",
                        "callback": "callback"
                    }
                }));
                log('申请权限');
            }
        }

        function callback(result) {
            alert('权限查询结果: ' + JSON.stringify(result));
        }

        function openUrl() {
            if (window.FlutterJSBridge) {
                window.FlutterJSBridge.postMessage(JSON.stringify({
                    method: 'openActionBrowser',
                    data: { url: 'https://flutter.dev' }
                }));
                log('请求打开浏览器');
            }
        }

        // 监听来自Flutter的消息
        window.onFlutterMessage = function(message) {
            log(`收到Flutter消息: ${JSON.stringify(message)}`);
        };

        // 页面加载完成
        document.addEventListener('DOMContentLoaded', function() {
            log('页面加载完成，等待Flutter桥接...');
        });

        // 监听桥接就绪
        window.addEventListener('flutterReady', function() {
            log('Flutter桥接已就绪!');
        });
    </script>
</body>
</html> 