import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../services/app_route_manager.dart';
import '../../providers/game_circle_provider.dart';

class GameCirclePage extends StatefulWidget {
  const GameCirclePage({super.key});

  @override
  State<GameCirclePage> createState() => _GameCirclePageState();
}

class _GameCirclePageState extends State<GameCirclePage> {
  // 模拟游戏数据
  final List<GameCircleItem> gameItems = [
    GameCircleItem(
      id: '1',
      name: '斗罗大陆：猎魂世界',
      imageUrl: 'assets/images/game_douluodalu.png',
      hasReward: true,
      rewardText: '必得体验金',
    ),
    GameCircleItem(
      id: '2',
      name: '斗罗大陆：猎魂世界',
      imageUrl: 'assets/images/game_douluodalu.png',
      hasReward: false,
    ),
    GameCircleItem(
      id: '3',
      name: '斗罗大陆：猎魂世界',
      imageUrl: 'assets/images/game_douluodalu.png',
      hasReward: false,
    ),
    GameCircleItem(
      id: '4',
      name: '斗罗大陆：猎魂世界',
      imageUrl: 'assets/images/game_douluodalu.png',
      hasReward: false,
    ),
    GameCircleItem(
      id: '5',
      name: '斗罗大陆：猎魂世界',
      imageUrl: 'assets/images/game_douluodalu.png',
      hasReward: false,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: const Text(
          '请选择游戏圈',
          style: TextStyle(
            color: Colors.black,
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: GridView.builder(
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 3,
            crossAxisSpacing: 12,
            mainAxisSpacing: 16,
            childAspectRatio: 0.75,
          ),
          itemCount: gameItems.length,
          itemBuilder: (context, index) {
            final game = gameItems[index];
            return GameCircleCard(
              game: game,
              onTap: () => _onGameTap(game),
            );
          },
        ),
      ),
    );
  }

  void _onGameTap(GameCircleItem game) async {
    // 处理游戏圈点击事件
    print('选择了游戏圈: ${game.name}');
    
    // 保存游戏圈选择状态
    final gameCircleProvider = Provider.of<GameCircleProvider>(context, listen: false);
    await gameCircleProvider.selectGameCircle(game.id, game.name);
    
    // 选择游戏圈后跳转到主页
    if (mounted) {
      AppRouteManager.navigateToHome(context);
    }
  }
}

class GameCircleCard extends StatelessWidget {
  final GameCircleItem game;
  final VoidCallback onTap;

  const GameCircleCard({
    super.key,
    required this.game,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // 游戏图片容器
            Expanded(
              flex: 3,
              child: Stack(
                children: [
                  Container(
                    width: double.infinity,
                    decoration: const BoxDecoration(
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(8),
                        topRight: Radius.circular(8),
                      ),
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Color(0xFF4A90E2),
                          Color(0xFF357ABD),
                        ],
                      ),
                    ),
                    child: ClipRRect(
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(8),
                        topRight: Radius.circular(8),
                      ),
                      child: Image.asset(
                        game.imageUrl,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            decoration: const BoxDecoration(
                              borderRadius: BorderRadius.only(
                                topLeft: Radius.circular(8),
                                topRight: Radius.circular(8),
                              ),
                              gradient: LinearGradient(
                                begin: Alignment.topCenter,
                                end: Alignment.bottomCenter,
                                colors: [
                                  Color(0xFF4A90E2),
                                  Color(0xFF357ABD),
                                ],
                              ),
                            ),
                            child: const Center(
                              child: Text(
                                'MMO',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                  // 奖励标签
                  if (game.hasReward)
                    Positioned(
                      top: 6,
                      right: 6,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 6,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: const Color(0xFFFF6B35),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: Text(
                          game.rewardText ?? '',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),
            // 游戏名称
            Expanded(
              flex: 1,
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
                child: Center(
                  child: Text(
                    game.name,
                    style: const TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      color: Color(0xFF333333),
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class GameCircleItem {
  final String id;
  final String name;
  final String imageUrl;
  final bool hasReward;
  final String? rewardText;

  GameCircleItem({
    required this.id,
    required this.name,
    required this.imageUrl,
    this.hasReward = false,
    this.rewardText,
  });
}