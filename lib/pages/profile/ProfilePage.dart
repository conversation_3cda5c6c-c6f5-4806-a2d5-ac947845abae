
import 'package:flutter/material.dart';
import '../settings/SettingsPage.dart';

class ProfilePage extends StatelessWidget {
  const ProfilePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Column(
        children: [
          // 顶部用户信息区
          Container(
            padding: const EdgeInsets.all(16),
            color: const Color(0xFFF5F5F5),
            child: Row(
              children: [
                // 用户头像
                Container(
                  width: 60,
                  height: 60,
                  decoration:
                      BoxDecoration(
                    shape: BoxShape.circle,
                    image: DecorationImage(
                      image: AssetImage('assets/images/avatar.png'), // 需要添加头像图片
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                // 用户信息
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children:
                    [
                      const Text(
                        '游戏玩家215590...',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children:
                        [
                          const Text(
                            '主页 > ',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey,
                            ),
                          ),
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 6, vertical: 2),
                            decoration: BoxDecoration(
                              color: Colors.blue,
                              borderRadius: BorderRadius.circular(10),
                            ),
                            child: const Text(
                              'Lv2',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                // 礼物和设置图标
                Row(
                  children:
                  [
                    IconButton(
                      icon: const Icon(Icons.card_giftcard),
                      onPressed: () {},
                    ),
                    IconButton(
                      icon: const Icon(Icons.settings),
                      onPressed: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const SettingsPage(),
                          ),
                        );
                      },
                    ),
                  ],
                ),
              ],
            ),
          ),

          // 功能图标区
          GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: 4,
            children: [
              _buildFunctionIcon('将军令', Icons.shield),
              _buildFunctionIcon('账号百宝箱', Icons.folder),
              _buildFunctionIcon('点卡充值', Icons.credit_card),
              _buildFunctionIcon('藏宝阁', Icons.lock),
              _buildFunctionIcon('我的收益', Icons.monetization_on),
              _buildFunctionIcon('我的客服', Icons.headset_mic),
              _buildFunctionIcon('创作中心', Icons.lightbulb),
              _buildFunctionIcon('限时领黑胶', Icons.music_note),
            ],
          ),

          // VIP进度条
          Container(
            margin: const EdgeInsets.all(16),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: const Color(0xFFE8F4F9),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Column(
              children:
              [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children:
                  [
                    const Text(
                      'VIP2 超级会员',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF0084FF),
                      ),
                    ),
                    TextButton(
                      onPressed: () {},
                      child: const Text(
                        '全部权益 >',
                        style: TextStyle(
                          fontSize: 14,
                          color: Color(0xFF0084FF),
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                LinearProgressIndicator(
                  value: 0.281, // 2810/10000
                  backgroundColor: Colors.grey[200],
                  valueColor: const AlwaysStoppedAnimation<Color>(
                    Color(0xFF0084FF),
                  ),
                ),
                const SizedBox(height: 4),
                const Align(
                  alignment: Alignment.centerRight,
                  child:
                  Text(
                    '2810/10000',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // 游戏信息区
          Container(
            margin: const EdgeInsets.symmetric(
                horizontal: 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children:
              [
                Row(
                  children:
                  [
                    Container(
                      width: 50,
                      height: 50,
                      decoration:
                          BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        image: const DecorationImage(
                          image: AssetImage('assets/images/sy37_ic_logo.png'), // 需要添加游戏图标
                          fit: BoxFit.cover,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    const Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children:
                      [
                        Text(
                          '第五人格',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          '大神登录礼 完成任务领奖励',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
                TextButton(
                  onPressed: () {},
                  child: const Text(
                    '切换游戏',
                    style: TextStyle(
                      fontSize: 14,
                      color: Color(0xFF0084FF),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // 功能图标构建方法
  Widget _buildFunctionIcon(String title, IconData icon) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children:
      [
        Icon(
          icon,
          size: 30,
          color: const Color(0xFF666666),
        ),
        const SizedBox(height: 8),
        Text(
          title,
          style: const TextStyle(
            fontSize: 12,
            color: Color(0xFF666666),
          ),
        ),
      ],
    );
  }

  // 奖励项构建方法
  Widget _buildRewardItem(String name, String imagePath) {
    return Container(
      margin: const EdgeInsets.only(right: 16),
      child:
      Column(
        children:
        [
          Container(
            width: 80,
            height: 80,
            decoration:
                BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              image: DecorationImage(
                image: AssetImage(imagePath),
                fit: BoxFit.cover,
              ),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            name,
            style: const TextStyle(
              fontSize: 14,
              color: Color(0xFF666666),
            ),
          ),
        ],
      ),
    );
  }

  // 游戏礼包构建方法
  Widget _buildGamePackage(String title, String subtitle, String imagePath) {
    return Expanded(
      child:
      Container(
        margin: const EdgeInsets.only(right: 8),
        padding: const EdgeInsets.all(16),
        decoration:
            BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          color: const Color(0xFFF5F5F5),
          image: DecorationImage(
            image: AssetImage(imagePath),
            fit: BoxFit.cover,
            opacity: 0.5,
          ),
        ),
        child:
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children:
          [
            Text(
              title,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.black,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: const TextStyle(
                fontSize: 14,
                color: Color(0xFF666666),
              ),
            ),
          ],
        ),
      ),
    );
  }
}