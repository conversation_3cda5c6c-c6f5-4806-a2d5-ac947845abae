import 'package:flutter/material.dart';
import '../../webview/webview_dialog.dart';
import 'AccountManagementPage.dart';
import '../../services/app_route_manager.dart';

class SettingsPage extends StatelessWidget {
  const SettingsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          '设置',
          style: TextStyle(
            color: Colors.black,
            fontSize: 18,
            fontWeight: FontWeight.w500,
          ),
        ),
        centerTitle: true,
      ),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  _buildSettingItem(context, '账号管理'),
                  _buildDivider(),
                  _buildSettingItem(context, '播放设置'),
                  _buildDivider(),
                  _buildSettingItem(context, '推送设置'),
                  _buildDivider(),
                  _buildSettingItem(context, '地址管理'),
                  _buildDivider(),
                  _buildSettingItemWithValue('清理缓存', '180.22MB'),
                  _buildDivider(),
                  _buildSettingItem(context, '建议与反馈'),
                  _buildDivider(),
                  _buildSettingItem(context, '检查更新'),
                  _buildDivider(),
                  _buildSettingItem(context, '关于我们'),
                  _buildDivider(),
                  _buildSettingItem(context, '个人信息收集清单'),
                  _buildDivider(),
                  _buildSettingItem(context, '第三方共享与个人信息清单'),
                  const SizedBox(height: 40),
                ],
              ),
            ),
          ),
          _buildLogoutButton(),
          const SizedBox(height: 34),
        ],
      ),
    );
  }

  Widget _buildSettingItem(BuildContext context, String title) {
    return GestureDetector(
      onTap: () {
        switch (title) {
          case '账号管理':
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const AccountManagementPage(),
              ),
            );
            break;
          case '建议与反馈':
            Navigator.of(context).push(
              PageRouteBuilder(
                opaque: false,
                barrierDismissible: true,
                barrierColor: Colors.transparent,
                pageBuilder: (context, animation, secondaryAnimation) {
                  return WebViewDialog(
                    url: "https://user.37.com.cn/sdkv1/service/home",
                    title: '官网',
                    showToolBar: true,
                  );
                },
              ),
            );
            break;
        }
      },
      child: Container(
        color: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              title,
              style: const TextStyle(
                fontSize: 16,
                color: Colors.black,
              ),
            ),
            const Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: Color(0xFFCCCCCC),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSettingItemWithValue(String title, String value) {
    return Container(
      color: Colors.white,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 16,
              color: Colors.black,
            ),
          ),
          Row(
            children: [
              Text(
                value,
                style: const TextStyle(
                  fontSize: 16,
                  color: Color(0xFF999999),
                ),
              ),
              const SizedBox(width: 8),
              const Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: Color(0xFFCCCCCC),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDivider() {
    return Container(
      color: Colors.white,
      child: Container(
        margin: const EdgeInsets.only(left: 16),
        height: 1,
        color: const Color(0xFFF0F0F0),
      ),
    );
  }

  Widget _buildLogoutButton() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      width: double.infinity,
      child: Builder(
        builder: (context) => TextButton(
          onPressed: () async {
            // 显示确认对话框
            final shouldLogout = await _showLogoutConfirmDialog(context);
            if (shouldLogout == true) {
              // 使用统一的退出登录方法
              AppRouteManager.navigateAfterLogout(context);
            }
          },
          style: TextButton.styleFrom(
            backgroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          child: const Text(
            '退出登录',
            style: TextStyle(
              fontSize: 16,
              color: Color(0xFF007AFF),
            ),
          ),
        ),
      ),
    );
  }

  Future<bool?> _showLogoutConfirmDialog(BuildContext context) {
    return showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认退出'),
        content: const Text('确定要退出当前账号吗？'),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text(
              '取消',
              style: TextStyle(color: Color(0xFF999999)),
            ),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text(
              '确定',
              style: TextStyle(color: Color(0xFF007AFF)),
            ),
          ),
        ],
      ),
    );
  }
}