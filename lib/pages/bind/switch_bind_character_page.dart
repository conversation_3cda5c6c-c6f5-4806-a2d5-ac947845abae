import 'package:flutter/material.dart';
import 'empty_bind_account_page.dart';

class SwitchBindCharacterPage extends StatefulWidget {
  const SwitchBindCharacterPage({super.key});

  @override
  State<SwitchBindCharacterPage> createState() => _SwitchBindCharacterPageState();
}

class _SwitchBindCharacterPageState extends State<SwitchBindCharacterPage> {
  // 模拟账号数据
  final List<AccountItem> _accounts = [
    AccountItem(
      phoneNumber: '***********',
      isCurrentLogin: true,
    ),
    AccountItem(
      phoneNumber: '***********',
      isCurrentLogin: false,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          '选择游戏账号绑定角色',
          style: TextStyle(
            color: Colors.black,
            fontSize: 18,
            fontWeight: FontWeight.w500,
          ),
        ),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 37手游官服账号
            const Text(
              '37手游官服账号',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 12),
            
            // 账号列表
            ...List.generate(_accounts.length, (index) {
              return _buildAccountItem(_accounts[index]);
            }),
            
            const SizedBox(height: 12),
            
            // 其他账密/手机账号
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(14),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: Row(
                children: [
                  const Expanded(
                    child: Text(
                      '其他账密/手机账号',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.black87,
                      ),
                    ),
                  ),
                  Icon(
                    Icons.arrow_forward_ios,
                    size: 16,
                    color: Colors.grey[600],
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 24),
            
            // 其他绑定方式
            const Text(
              '其他绑定方式',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 12),
            
            // 游戏图标和名称
            Center(
              child: Column(
                children: [
                  Container(
                    width: 70,
                    height: 70,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(14),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.grey.withOpacity(0.2),
                          spreadRadius: 1,
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(14),
                      child: Image.network(
                        'https://imgcs.s98s2.com/common/1747279036火舞头像.png',
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            color: Colors.grey[200],
                            child: const Icon(
                              Icons.games,
                              size: 35,
                              color: Colors.grey,
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    '斗罗大陆：魂师对决',
                    style: TextStyle(
                      fontSize: 15,
                      fontWeight: FontWeight.w500,
                      color: Colors.black87,
                    ),
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 24),
            
            // 拉起游戏授权绑定按钮
            Stack(
              clipBehavior: Clip.none,
              children: [
                Container(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: _launchGameAuthorization,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue[600],
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 14),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      elevation: 0,
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.image_outlined,
                          size: 18,
                        ),
                        const SizedBox(width: 6),
                        const Text(
                          '拉起游戏授权绑定',
                          style: TextStyle(
                            fontSize: 15,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                // 叠在右上角的标签
                Positioned(
                  top: -8,
                  right: 8,
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 3),
                    decoration: BoxDecoration(
                      color: Colors.red,
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: const Text(
                      '非37官方账号推荐使用',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 9,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 8),
            
            // 底部提示文字
            const Center(
              child: Text(
                '选择授权游戏包后游戏内点击【确定】',
                style: TextStyle(
                  fontSize: 11,
                  color: Colors.grey,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAccountItem(AccountItem account) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: GestureDetector(
        onTap: () => _selectAccount(account),
        child: Container(
          padding: const EdgeInsets.all(14),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.1),
                spreadRadius: 1,
                blurRadius: 3,
                offset: const Offset(0, 1),
              ),
            ],
          ),
          child: Row(
            children: [
              Expanded(
                child: Text(
                  account.phoneNumber,
                  style: const TextStyle(
                    fontSize: 15,
                    color: Colors.black87,
                  ),
                ),
              ),
              if (account.isCurrentLogin)
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.blue[600],
                    borderRadius: BorderRadius.circular(14),
                  ),
                  child: const Text(
                    '当前登录',
                    style: TextStyle(
                      fontSize: 11,
                      color: Colors.white,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                )
              else
                Icon(
                  Icons.arrow_forward_ios,
                  size: 14,
                  color: Colors.grey[600],
                ),
            ],
          ),
        ),
      ),
    );
  }

  void _selectAccount(AccountItem account) {
    if (account.isCurrentLogin) {
      // 当前登录账号，跳转到空绑定页面
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => EmptyBindAccountPage(
            phoneNumber: account.phoneNumber,
          ),
        ),
      );
      return;
    }
    
    // 其他账号也跳转到空绑定页面
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => EmptyBindAccountPage(
          phoneNumber: account.phoneNumber,
        ),
      ),
    );
  }

  void _performAccountSwitch(AccountItem account) {
    // TODO: 实现实际的账号切换逻辑
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('已切换到账号 ${account.phoneNumber}'),
        backgroundColor: Colors.green,
      ),
    );
    
    // 更新当前登录状态
    setState(() {
      for (var acc in _accounts) {
        acc.isCurrentLogin = acc.phoneNumber == account.phoneNumber;
      }
    });
  }

  void _launchGameAuthorization() {
    // TODO: 实现拉起游戏授权绑定功能
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('拉起游戏授权绑定'),
          content: const Text('正在启动游戏进行授权绑定...'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('确定'),
            ),
          ],
        );
      },
    );
  }
}

class AccountItem {
  final String phoneNumber;
  bool isCurrentLogin;

  AccountItem({
    required this.phoneNumber,
    required this.isCurrentLogin,
  });
}