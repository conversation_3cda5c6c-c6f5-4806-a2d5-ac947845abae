import 'package:flutter/material.dart';
import 'dart:async';
import 'package:provider/provider.dart';
import '../../providers/user_provider.dart';
import '../../providers/app_review_provider.dart';
import '../../services/app_route_manager.dart';
import '../../net/api/init_service.dart';
import '../../net/api/app_review_service.dart';
import '../../utils/log_util.dart';

class SplashPage extends StatefulWidget {
  const SplashPage({Key? key}) : super(key: key);

  @override
  _SplashPageState createState() => _SplashPageState();
}

class _SplashPageState extends State<SplashPage> with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late AnimationController _scaleController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;
  
  // 用于取消异步操作的标志
  bool _isDisposed = false;



  @override
  void initState() {
    super.initState();

    // 初始化动画控制器
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    // 创建渐变动画
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    // 创建缩放动画
    _scaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(parent: _scaleController, curve: Curves.easeOutBack),
    );

    // 启动动画
    _fadeController.forward();
    _scaleController.forward();

    // 执行所有异步操作，完成后再跳转
    _initializeApp();
  }

  /// 初始化应用 - 执行所有异步操作后再跳转
  Future<void> _initializeApp() async {
    LogUtil.d('开始应用初始化...');
    
    try {
      // 执行所有激活接口（M层 + S层）
      await _callActivateAPI();
      
      if (_isDisposed || !mounted) return;
      
      // 确保最少显示时间（提供更好的用户体验）
      LogUtil.d('等待最少显示时间...');
      await Future.delayed(const Duration(seconds: 1));
      
      if (_isDisposed || !mounted) return;
      
      // 所有操作完成后进行跳转
      LogUtil.d('应用初始化完成，准备跳转...');
      await _navigateWithAnimation();
      
    } catch (e) {
      LogUtil.e('应用初始化失败: $e');
      
      // 即使出错也要跳转，避免卡在启动页
      if (!_isDisposed && mounted) {
        LogUtil.d('初始化失败，执行兜底跳转...');
        await _navigateWithAnimation();
      }
    }
  }

  Future<void> _callActivateAPI() async {
    try {
      if (_isDisposed || !mounted) return;
      
      LogUtil.d('开始M层激活...');
      final mResponse = await InitService.mActivate(context: context);
      if (_isDisposed || !mounted) return;
      
      LogUtil.e("M层激活 ${mResponse.toString()}");
      if (mResponse.success && mResponse.data != null) {
        LogUtil.d('M层激活成功');
        
        // 执行S层激活接口
        if (!_isDisposed && mounted) {
          LogUtil.d('开始S层激活...');
          await _callSActivateAPI();
        }
      } else {
        LogUtil.d('M层激活失败: ${mResponse.message}');
      }
      
      // 激活完成后调用过审接口
      if (!_isDisposed && mounted) {
        LogUtil.d('开始过审状态检查...');
        await _callAppReviewAPI();
      }
      
      LogUtil.d('所有激活接口调用完成');
    } catch (e) {
      LogUtil.e('激活接口调用异常: $e');
    }
  }

  Future<void> _callSActivateAPI() async {
    try {
      if (_isDisposed || !mounted) return;
      final response = await InitService.sActivate(context: context);
      if (_isDisposed || !mounted) return;
      
      LogUtil.e("S层激活 ${response.toString()}");
      if (response.success && response.data != null) {
        LogUtil.d('S层激活成功');
      } else {
        LogUtil.d('S层激活失败: ${response.message}');
      }
    } catch (e) {
      LogUtil.e('S层激活接口调用异常: $e');
    }
  }

  Future<void> _callAppReviewAPI() async {
    try {
      if (_isDisposed || !mounted) return;
      final response = await AppReviewService.checkReviewStatus(context: context);
      if (_isDisposed || !mounted) return;
      
      LogUtil.d("过审状态检查 ${response.toString()}");
      if (response.success && response.data != null) {
        final inReview = response.data!.inReview;
        LogUtil.d('过审状态检查成功，状态: ${inReview ? "审核中" : "正常"}');
        
        // 更新Provider中的过审状态
        final appReviewProvider = Provider.of<AppReviewProvider>(context, listen: false);
        appReviewProvider.updateReviewStatus(inReview);
      } else {
        LogUtil.d('过审状态检查失败: ${response.message}');
      }
    } catch (e) {
      LogUtil.e('过审状态检查接口调用异常: $e');
    }
  }



  Future<void> _navigateWithAnimation() async {
    if (_isDisposed || !mounted) return;
    
    LogUtil.d('开始页面跳转...');
    
    // 初始化用户状态
    try {
      final userProvider = Provider.of<UserProvider>(context, listen: false);
      await userProvider.initialize();
    } catch (e) {
      LogUtil.e('用户状态初始化失败: $e');
    }

    if (_isDisposed || !mounted) return;
    
    // 先执行淡出动画
    await _fadeController.reverse();

    // 使用路由管理器进行跳转
    if (!_isDisposed && mounted) {
      LogUtil.d('执行路由跳转...');
      await AppRouteManager.checkLoginNavigate(context);
    }
  }

  @override
  void dispose() {
    _isDisposed = true; // 标记为已销毁，阻止后续异步操作
    _fadeController.dispose();
    _scaleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: ScaleTransition(
          scale: _scaleAnimation,
          child: Container(
            width: double.infinity, // 全屏宽度
            height: double.infinity, // 全屏高度
            decoration: const BoxDecoration(
              image: DecorationImage(
                image: AssetImage('assets/images/dlyz_splash.png'),
                fit: BoxFit.cover, // 填充整个屏幕
              ),
            ),
          ),
        ),
      ),
    );
  }
}