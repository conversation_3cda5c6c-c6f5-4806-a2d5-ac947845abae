import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';

import '../../common/dl_color.dart';
import '../../net/api/forum_service.dart';
import '../../net/config/http_base_config.dart';

/// 举报弹窗返回结果
class ReportResult {
  final String reasonId;
  final String reasonText;
  final String? extra;

  ReportResult({required this.reasonId, required this.reasonText, this.extra});
}

/// 显示举报弹窗
/// 返回 [ReportResult]，取消时返回 null
Future<ReportResult?> showReportDialog(
  BuildContext context, {
  required int threadId,
  required int userId,
  List<String>? defaultReasons,
}) {
  return showDialog<ReportResult>(
    context: context,
    barrierDismissible: true,
    builder: (context) => _ReportDialog(
      threadId: threadId,
      userId: userId,
      defaultReasons: defaultReasons,
    ),
  );
}

class _ReportDialog extends StatefulWidget {
  final int threadId;
  final int userId;
  final List<String>? defaultReasons;

  const _ReportDialog({
    required this.threadId,
    required this.userId,
    this.defaultReasons,
  });

  @override
  State<_ReportDialog> createState() => _ReportDialogState();
}

class _ReportDialogState extends State<_ReportDialog> {
  late final List<_ReasonItem> _reasons;
  String? _selectedId;
  final TextEditingController _controller = TextEditingController();

  @override
  void initState() {
    super.initState();
    final defaults =
        widget.defaultReasons ??
        const ['有垃圾或广告信息', '有政治敏感信息', '有暴力黄色信息', '骚扰辱骂，传播封建迷信', '内容涉及侵权', '其他'];

    _reasons = [
      for (int i = 0; i < defaults.length; i++)
        _ReasonItem(
          id: 'r$i',
          title: defaults[i],
          isOther: defaults[i] == '其他',
        ),
    ];
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  bool get _isOtherSelected =>
      _selectedId != null &&
      _reasons.firstWhere((e) => e.id == _selectedId!).isOther;

  void _onSelect(String id) {
    setState(() {
      _selectedId = id;
      if (!_isOtherSelected) {
        _controller.clear();
      }
    });
  }

  void _onConfirm() {
    if (_selectedId == null) return;
    final selected = _reasons.firstWhere((e) => e.id == _selectedId!);
    if (selected.isOther && _controller.text.trim().isEmpty) {
      // 需要补充说明
      return;
    }

    final String reasonText = selected.isOther ? _controller.text.trim() : selected.title;

    () async {
      final resp = await ForumService().reportThread(
        baseUrl: HttpBaseConfig.forumBaseUrl,
        threadId: widget.threadId,
        userId: widget.userId,
        reason: reasonText,
      );

      if (!mounted) return;
      Fluttertoast.showToast(msg: resp.message);

      Navigator.of(context).pop(
        ReportResult(
          reasonId: selected.id,
          reasonText: selected.title,
          extra: _isOtherSelected ? _controller.text.trim() : null,
        ),
      );
    }();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Dialog(
      backgroundColor: Colors.white,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      insetPadding: const EdgeInsets.symmetric(horizontal: 24),
      child: ConstrainedBox(
        constraints: const BoxConstraints(maxHeight: 560),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 18, 16, 10),
              child: Text(
                '请选择您举报的原因',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w700,
                ),
                textAlign: TextAlign.center,
              ),
            ),
            Flexible(
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    for (int i = 0; i < _reasons.length; i++) ...[
                      _buildReasonTile(_reasons[i]),
                      if (i < _reasons.length - 1) _divider(),
                    ],
                    if (_isOtherSelected) _buildOtherInput(),
                  ],
                ),
              ),
            ),
            Divider(height: 1, thickness: 1, color: DLColor.divider),
            SizedBox(
              height: 52,
              child: Row(
                children: [
                  Expanded(
                    child: TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      child: const Text('取消'),
                    ),
                  ),
                  Container(
                    width: 1,
                    height: double.infinity,
                    color: DLColor.divider,
                  ),
                  Expanded(
                    child: TextButton(
                      onPressed:
                          _selectedId == null
                              ? null
                              : () {
                                if (_isOtherSelected &&
                                    _controller.text.trim().isEmpty)
                                  return;
                                _onConfirm();
                              },
                      child: const Text('确定'),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 自定义分割线：颜色 #EAECF1，左右与文字对齐（与 ListTile padding 一致）
  Widget _divider() {
    return Divider(
      height: 1,
      thickness: 1,
      indent: 16,
      endIndent: 16,
      color: DLColor.divider,
    );
  }

  Widget _buildReasonTile(_ReasonItem item) {
    final selected = _selectedId == item.id;
    return ListTile(
      contentPadding: const EdgeInsets.symmetric(horizontal: 16),
      title: Text(item.title, style: const TextStyle(fontSize: 14)),
      trailing: GestureDetector(
        onTap: () => _onSelect(item.id),
        behavior: HitTestBehavior.opaque,
        child: Padding(
          padding: const EdgeInsets.all(4),
          child: Image.asset(
            selected
                ? 'assets/images/checked_icon.png'
                : 'assets/images/unchecked_icon.png',
            width: 20,
            height: 20,
            fit: BoxFit.contain,
          ),
        ),
      ),
      onTap: () => _onSelect(item.id),
    );
  }

  Widget _buildOtherInput() {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 8, 16, 12),
      child: TextField(
        controller: _controller,
        maxLines: 4,
        decoration: InputDecoration(
          hintText: '请输入举报原因',
          hintStyle: const TextStyle(fontSize: 14, color: Colors.black38),
          filled: true,
          fillColor: Colors.grey[100],
          contentPadding: const EdgeInsets.all(12),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: BorderSide(color: Colors.grey[300]!),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: BorderSide(color: Colors.grey[400]!),
          ),
        ),
      ),
    );
  }
}

class _ReasonItem {
  final String id;
  final String title;
  final bool isOther;

  _ReasonItem({required this.id, required this.title, this.isOther = false});
}
