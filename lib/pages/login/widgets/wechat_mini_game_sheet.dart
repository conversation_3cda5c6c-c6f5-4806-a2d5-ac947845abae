import 'package:flutter/material.dart';

import '../../../manager/channel_manager.dart';
import '../../../model/mini_program_info.dart';

/// 微信小游戏选择弹窗
class WechatMiniGameSheet extends StatelessWidget {
  final List<_MiniGameOption> options;

  const WechatMiniGameSheet({super.key, required this.options});

  static Future<void> show(BuildContext context) async {
    final List<_MiniGameOption> defaultOptions = [
      const _MiniGameOption(
        title: '灵魂序章',
        imageAsset: 'assets/images/avatar.png',
        schemeUrl: 'weixin://dl/business/?t=89bSVHqqwNa',
      ),
      const _MiniGameOption(
        title: '斗罗大陆H5',
        imageAsset: 'assets/images/avatar.png',
        schemeUrl: 'weixin://dl/business/?t=89bSVHqqwNa',
      ),
      const _MiniGameOption(
        title: '斗罗大陆:武魂觉醒',
        imageAsset: 'assets/images/avatar.png',
        schemeUrl: 'weixin://dl/business/?t=89bSVHqqwNa',
      ),
    ];

    await showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (_) => WechatMiniGameSheet(options: defaultOptions),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          const SizedBox(height: 12),
          Center(
            child: Container(
              width: 120,
              height: 6,
              decoration: BoxDecoration(
                color: const Color(0xFFE6E6E6),
                borderRadius: BorderRadius.circular(3),
              ),
            ),
          ),
          const SizedBox(height: 16),
          const Center(
            child: Text(
              '请选择要登录的微信小游戏',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Color(0xFF3D3D3D),
              ),
            ),
          ),
          const SizedBox(height: 32),
          GridView.builder(
            padding: const EdgeInsets.symmetric(horizontal: 24),
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 3,
              mainAxisSpacing: 16,
              crossAxisSpacing: 16,
              childAspectRatio: 0.78,
            ),
            itemCount: options.length,
            itemBuilder: (context, index) {
              final item = options[index];
              return _MiniGameItem(
                option: item,
                onTap: () async {
                  Navigator.of(context).pop();
                  await ChannelManager().jumpToMiniProgram(
                    info: MiniProgramInfo(
                      skipType: '2',
                      miniProgramId: '',
                      miniProgramPath: '',
                      schemeUrl: item.schemeUrl,
                    ),
                  );
                },
              );
            },
          ),
          const SizedBox(height: 16),
        ],
      ),
    );
  }
}

class _MiniGameItem extends StatelessWidget {
  final _MiniGameOption option;
  final VoidCallback onTap;

  const _MiniGameItem({
    required this.option,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          ClipOval(
            child: Image.asset(
              option.imageAsset,
              width: 72,
              height: 72,
              fit: BoxFit.cover,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            option.title,
            style: const TextStyle(
              fontSize: 12,
              color: Color(0xFF303133),
              height: 1.2,
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }
}

class _MiniGameOption {
  final String title;
  final String imageAsset;
  final String schemeUrl;

  const _MiniGameOption({
    required this.title,
    required this.imageAsset,
    required this.schemeUrl,
  });
}


