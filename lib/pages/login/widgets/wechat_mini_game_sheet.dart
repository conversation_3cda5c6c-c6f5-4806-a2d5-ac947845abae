import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:provider/provider.dart';
import 'package:fluttertoast/fluttertoast.dart';

import '../../../manager/channel_manager.dart';
import '../../../model/mini_program_info.dart';
import '../../../model/auth_app_item.dart';
import '../../../services/login_api_service.dart';
import '../../../providers/user_provider.dart';
import '../../../config/app_config.dart';

/// 微信小游戏选择弹窗
class WechatMiniGameSheet extends StatelessWidget {
  final List<_MiniGameOption> options;

  const WechatMiniGameSheet({super.key, required this.options});

  static Future<void> show(BuildContext context, {List<AuthAppItem>? authAppList}) async {
    // 如果传入了授权应用列表，使用该列表；否则使用默认选项
    final List<_MiniGameOption> options;

    if (authAppList != null && authAppList.isNotEmpty) {
      // 使用API返回的授权应用列表
      options =
          authAppList
              .map((app) => _MiniGameOption(title: app.name, imageUrl: app.icon, miniProgramId: app.wechatMinigramId))
              .toList();
    } else {
      // 使用默认选项
      options = [];
    }

    await showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (_) => WechatMiniGameSheet(options: options),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(color: Colors.white, borderRadius: BorderRadius.vertical(top: Radius.circular(20))),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          const SizedBox(height: 12),
          Center(
            child: Container(
              width: 120,
              height: 6,
              decoration: BoxDecoration(color: const Color(0xFFE6E6E6), borderRadius: BorderRadius.circular(3)),
            ),
          ),
          const SizedBox(height: 16),
          const Center(
            child: Text('请选择要登录的微信小游戏', style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500, color: Color(0xFF3D3D3D))),
          ),
          const SizedBox(height: 32),
          GridView.builder(
            padding: const EdgeInsets.symmetric(horizontal: 24),
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 3,
              mainAxisSpacing: 16,
              crossAxisSpacing: 16,
              childAspectRatio: 0.78,
            ),
            itemCount: options.length,
            itemBuilder: (context, index) {
              final item = options[index];
              return _MiniGameItem(
                option: item,
                onTap: () async {
                  Navigator.of(context).pop();

                  try {
                    // 根据不同类型的配置信息跳转
                    if (item.miniProgramId.isNotEmpty) {
                      // 使用小程序ID调用getSkipAppletInfo接口
                      final flagId = int.tryParse(item.miniProgramId);
                      if (flagId != null) {
                        final api = LoginApiService();
                        final response = await api.getSkipAppletInfo(
                          pid: int.parse(AppConfig.pid),
                          gid: int.parse(AppConfig.gid),
                          flagId: flagId,
                        );
                        if (response.success && response.data != null) {
                          final skipInfo = response.data!;
                          // 直接使用返回的MiniProgramInfo进行跳转
                          await ChannelManager().jumpToMiniProgram(info: skipInfo);
                        } else {
                          // 接口调用失败，显示错误信息
                          if (context.mounted) {
                            _showErrorToast(response.message);
                          }
                        }
                      } else {
                        // flagId解析失败
                        if (context.mounted) {
                          Navigator.of(context).pop();
                          _showErrorToast('小程序ID格式错误');
                        }
                      }
                    } else {
                      // 关闭加载对话框
                      if (context.mounted) {
                        Navigator.of(context).pop();
                        _showErrorToast('缺少跳转信息');
                      }
                    }
                  } catch (e) {
                    // 关闭加载对话框
                    if (context.mounted) {
                      Navigator.of(context).pop();
                      _showErrorToast('跳转失败: $e');
                    }
                  }
                },
              );
            },
          ),
          const SizedBox(height: 16),
        ],
      ),
    );
  }

  /// 显示错误Toast
  static void _showErrorToast(String message) {
    Fluttertoast.showToast(
      msg: message,
      toastLength: Toast.LENGTH_LONG,
      gravity: ToastGravity.CENTER,
      backgroundColor: Colors.red,
      textColor: Colors.white,
      fontSize: 16.0,
    );
  }
}

class _MiniGameItem extends StatelessWidget {
  final _MiniGameOption option;
  final VoidCallback onTap;

  const _MiniGameItem({required this.option, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          ClipOval(child: _buildImage()),
          const SizedBox(height: 12),
          Text(
            option.title,
            style: const TextStyle(fontSize: 12, color: Color(0xFF303133), height: 1.2),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildImage() {
    if (option.imageUrl != null && option.imageUrl!.isNotEmpty) {
      // 使用网络图片
      return CachedNetworkImage(
        imageUrl: option.imageUrl!,
        width: 72,
        height: 72,
        fit: BoxFit.cover,
        placeholder:
            (context, url) =>
                Container(width: 72, height: 72, color: Colors.grey[200], child: const Icon(Icons.image, color: Colors.grey)),
        errorWidget:
            (context, url, error) => Container(
              width: 72,
              height: 72,
              color: Colors.grey[200],
              child: const Icon(Icons.broken_image, color: Colors.grey),
            ),
      );
    } else {
      // 默认占位图
      return Container(width: 72, height: 72, color: Colors.grey[200], child: const Icon(Icons.games, color: Colors.grey));
    }
  }
}

class _MiniGameOption {
  final String title;
  final String? imageUrl;
  final String miniProgramId;

  const _MiniGameOption({required this.title, this.imageUrl, this.miniProgramId = ''});
}
