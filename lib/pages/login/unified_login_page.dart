import 'package:dlyz_flutter/net/http_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:fluttertoast/fluttertoast.dart';
import '../../providers/login_provider.dart';
import '../../services/app_route_manager.dart';
import '../../main.dart';
import 'widgets/account_login_form.dart';
import 'widgets/phone_login_form.dart';
import 'widgets/phone_password_login_form.dart';
/// 统一登录页面 - 合并原LoginPage和LoginManager功能
class UnifiedLoginPage extends StatefulWidget {
  final VoidCallback? onLoginSuccess;
  final bool directPop;

  const UnifiedLoginPage({
    super.key,
    this.onLoginSuccess,
    this.directPop = false,
  });

  @override
  State<UnifiedLoginPage> createState() => _UnifiedLoginPageState();
}

class _UnifiedLoginPageState extends State<UnifiedLoginPage>
    with TickerProviderStateMixin {
  static const double _formOverlap = 15;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    
    // 设置状态栏样式
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
        statusBarBrightness: Brightness.light,
      ),
    );
    
    // 初始化动画
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0.0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));
    
    // 初始化登录状态
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final loginProvider = Provider.of<LoginStateProvider>(context, listen: false);
      loginProvider.initializeLoginFlow(context);
      _animationController.forward();
    });
    HttpService.getInstance().setNavigatorKey(navigatorKey);
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (bool didPop, dynamic result) {
        if (!didPop) {
          _handleBackPressed();
        }
      },
      child: Scaffold(
        backgroundColor: Colors.white,
        resizeToAvoidBottomInset: false, // 防止键盘顶起底部内容
        body: SafeArea(
          // 登录页需要头图铺满到状态栏下方，因此关闭顶部安全区；
          // 仍保留底部安全区以避免 Home 指示条遮挡。
          top: false,
          child: AnimatedBuilder(
            animation: _animationController,
            builder: (context, child) {
              return FadeTransition(
                opacity: _fadeAnimation,
                child: SlideTransition(
                  position: _slideAnimation,
                  child: Consumer<LoginStateProvider>(
                    builder: (context, loginProvider, child) {
                      return Container(
                        decoration: const BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: [
                              Color(0xFF6B73FF),
                              Color(0xFF9DD5FF),
                            ],
                          ),
                        ),
                        child: Stack(
                          children: [
                            Column(
                              children: [
                                _welcomeAreaWithBanner(),
                                Expanded(
                                  child: OverflowBox(
                                    maxHeight: double.infinity,
                                    child: Transform.translate(
                                      offset: const Offset(0, -_formOverlap),
                                      child: Container(
                                        decoration: const BoxDecoration(
                                          color: Colors.white,
                                          borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
                                        ),
                                        child: _buildContent(loginProvider),
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            Positioned(
                              left: 0,
                              right: 0,
                              bottom: 0,
                              child: Container(
                                height: _formOverlap,
                                color: Colors.white,
                              ),
                            ),
                            if (loginProvider.isFastLoginInProgress)
                              Positioned.fill(
                                child: IgnorePointer(
                                  child: Container(
                                    color: Colors.white.withOpacity(0.8),
                                    child: const Center(
                                      child: CircularProgressIndicator(),
                                    ),
                                  ),
                                ),
                              ),
                          ],
                        ),
                      );
                    },
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildContent(LoginStateProvider loginProvider) {
    // 根据登录状态显示不同内容
    switch (loginProvider.currentState) {
      case LoginState.loading:
        return _buildLoadingView();
      
      case LoginState.accountLogin:
        return AccountLoginForm(
          onSwitchToPhone: () => loginProvider.switchToPhoneLogin(),
          onLoginSuccess: () => _handleLoginSuccess(),
          onForgetPassword: () => loginProvider.switchToPhoneLogin(),
        );
      
      case LoginState.phoneLogin:
        return PhoneLoginForm(
          onSwitchToAccount: () => loginProvider.switchToAccountLogin(),
          onSwitchToPhonePassword: () => loginProvider.switchToPhonePasswordLogin(),
          onSendCode: (phone) => loginProvider.switchToVerifyCode(phone),
          onLoginSuccess: () => _handleLoginSuccess(),
        );

      case LoginState.phonePasswordLogin:
        return PhonePasswordLoginForm(
          onSwitchToPhoneCode: () => loginProvider.switchToPhoneLogin(),
          onSwitchToAccount: () => loginProvider.switchToAccountLogin(),
          onLoginSuccess: () => _handleLoginSuccess(),
        );
      
      case LoginState.wechatLogin:
        return _buildWechatLoginView();
      default:
        return _buildLoadingView();
    }
  }

  Widget _welcomeAreaWithBanner({double? height}) {
    return SizedBox(
      width: double.infinity,
      height: height,
      child: GestureDetector(
        onTap: () {
          Navigator.pushNamed(context, '/proxy-config');
        },
        child: Image.asset(
          'assets/images/login_top_banner.png',
          fit: BoxFit.fitWidth,
          alignment: Alignment.topCenter,
        ),
      ),
    );
  }

  Widget _buildLoadingView() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.blue[50]!,
            Colors.white,
          ],
        ),
      ),
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 20),
            Text(
              '正在初始化登录...',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWechatLoginView() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.green[50]!,
            Colors.white,
          ],
        ),
      ),
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.wechat,
              size: 80,
              color: Colors.green,
            ),
            SizedBox(height: 20),
            CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Colors.green),
            ),
            SizedBox(height: 20),
            Text(
              '正在进行微信登录...',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
            SizedBox(height: 10),
            Text(
              '请在微信中确认登录',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _handleLoginSuccess() {
    // 直接跳转，不延迟
    if (mounted) {
      widget.onLoginSuccess?.call();
      
      // 如果需要直接返回，则pop并传递成功结果
      if (widget.directPop) {
        Navigator.of(context).pop(true);
      } else {
        AppRouteManager.navigateAfterLogin(context);
      }
    }
  }


  void _handleBackPressed() {
    final loginProvider = Provider.of<LoginStateProvider>(context, listen: false);
    // 交给 Provider 的历史栈处理返回；未消费则弹退出
    final handled = loginProvider.back();
    if (!handled) {
      _showExitConfirmation();
    }
  }

  void _showExitConfirmation() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              Icon(Icons.help_outline, color: Colors.orange[600]),
              const SizedBox(width: 8),
              const Text('提示'),
            ],
          ),
          content: const Text('您还未登录，请选择继续登录或退出应用'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                '继续登录',
                style: TextStyle(color: Theme.of(context).primaryColor),
              ),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                SystemNavigator.pop();
              },
              style: TextButton.styleFrom(foregroundColor: Colors.red),
              child: const Text('退出应用'),
            ),
          ],
        );
      },
    );
  }
}