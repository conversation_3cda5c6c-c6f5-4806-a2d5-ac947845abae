import 'dart:io';

import 'package:dlyz_flutter/net/http_service.dart';
import 'package:dlyz_flutter/utils/log_util.dart';
import 'package:flutter/widgets.dart';
import 'package:getuiflut/getuiflut.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:provider/provider.dart';

import '../config/app_config.dart';
import '../net/transformers/response_transformers.dart';
import '../providers/user_provider.dart';

class SqPushManager {
  static final SqPushManager _instance = SqPushManager._internal();

  factory SqPushManager() => _instance;

  final HttpService _httpService = HttpService();
  final String _pushHost = "http://app-liefer.37.com.cn";

  bool _isInitPush = false;

  SqPushManager._internal();

  String _payloadInfo = 'Null';
  String _userMsg = "";
  String _notificationState = "";
  String _getClientId = "";
  String _getDeviceToken = "";
  String _onReceivePayload = "";
  String _onReceiveNotificationResponse = "";
  String _onAppLinkPayLoad = "";

  void init() {
    _initPushConfig();
  }

  Future<void> _initPushConfig() async {
    PackageInfo info = await PackageInfo.fromPlatform();
    Map<String, String> queryParams = {
      'apk_name': info.packageName,
      'pid': AppConfig.pid,
      'gid': AppConfig.gid,
    };
    final response = await _httpService.get(
      '/app/push/param',
      baseUrl: _pushHost,
      queryParameters: queryParams,
      parseStrategy: ResponseTransformers.codeData(),
      fromJsonT: (data) {
        return data;
      },
    );
    if (response.code == 0 && response.data != null) {
      // 初始化个推SDK
      LogUtil.d('开始初始化个推sdk');
      _initGetuiSdk();
      _isInitPush = true;
    }
  }

  Future<void> tryPushToken(String uid) async {
    if (!_isInitPush) {
      LogUtil.d('个推SDK未初始化，跳过推送token');
      return;
    }
    LogUtil.d('个推SDK开始推送token');
    PackageInfo info = await PackageInfo.fromPlatform();
    Map<String, String> data = {
      'apk_name': info.packageName,
      'pid': AppConfig.pid,
      'gid': AppConfig.gid,
      'cid': _getClientId,
      'uid': uid,
      'rid': uid,
    };
    final response = await _httpService.post(
      '/app/push/alias',
      baseUrl: _pushHost,
      data: data,
      parseStrategy: ResponseTransformers.codeData(),
      fromJsonT: (data) {
        return data;
      },
    );
    if (response.code == 0) {
      LogUtil.d('推送token成功');
    } else {
      LogUtil.d('推送token失败: ${response.message}');
    }
  }

  Future<void> _initGetuiSdk() async {
    try {
      if (Platform.isAndroid) {
        Getuiflut.initGetuiSdk;
      } else if (Platform.isIOS) {
        Getuiflut().startSdk(
          appId: "Rti8eljkzx9ojQNDbAlJp3",
          appKey: "C7V5lk3WK46sLY0PbuJkw3",
          appSecret: "YXYqQP3ADU73ZBEyfh3YU9",
        );
      }
      addEventHandler();
    } catch (e) {
      e.toString();
    }
  }

  void addEventHandler() {
    Getuiflut().addEventHandler(
      onReceiveClientId: (String message) async {
        LogUtil.d("flutter onReceiveClientId: $message");
        _getClientId = "$message";
      },
      onReceiveMessageData: (Map<String, dynamic> msg) async {
        LogUtil.d("flutter onReceiveMessageData: $msg");
        _payloadInfo = msg['payload'];
      },
      onNotificationMessageArrived: (Map<String, dynamic> msg) async {
        LogUtil.d("flutter onNotificationMessageArrived: $msg");
        _notificationState = 'Arrived';
      },
      onNotificationMessageClicked: (Map<String, dynamic> msg) async {
        LogUtil.d("flutter onNotificationMessageClicked: $msg");
        _notificationState = 'Clicked';
      },
      onTransmitUserMessageReceive: (Map<String, dynamic> msg) async {
        LogUtil.d("flutter onTransmitUserMessageReceive:$msg");
        _userMsg = msg["msg"];
      },
      onRegisterDeviceToken: (String message) async {
        LogUtil.d("flutter onRegisterDeviceToken: $message");
        _getDeviceToken = "$message";
      },
      onReceivePayload: (Map<String, dynamic> message) async {
        LogUtil.d("flutter onReceivePayload: $message");
        _onReceivePayload = "$message";
      },
      onReceiveNotificationResponse: (Map<String, dynamic> message) async {
        LogUtil.d("flutter onReceiveNotificationResponse: $message");
        _onReceiveNotificationResponse = "$message";
      },
      onAppLinkPayload: (String message) async {
        LogUtil.d("flutter onAppLinkPayload: $message");
        _onAppLinkPayLoad = "$message";
      },
      onPushModeResult: (Map<String, dynamic> message) async {
        LogUtil.d("flutter onPushModeResult: $message");
      },
      onSetTagResult: (Map<String, dynamic> message) async {
        LogUtil.d("flutter onSetTagResult: $message");
      },
      onAliasResult: (Map<String, dynamic> message) async {
        LogUtil.d("flutter onAliasResult: $message");
      },
      onQueryTagResult: (Map<String, dynamic> message) async {
        LogUtil.d("flutter onQueryTagResult: $message");
      },
      onWillPresentNotification: (Map<String, dynamic> message) async {
        LogUtil.d("flutter onWillPresentNotification: $message");
      },
      onOpenSettingsForNotification: (Map<String, dynamic> message) async {
        LogUtil.d("flutter onOpenSettingsForNotification: $message");
      },
      onGrantAuthorization: (String granted) async {
        LogUtil.d("flutter onGrantAuthorization: $granted");
      },
      onLiveActivityResult: (Map<String, dynamic> message) async {
        LogUtil.d("flutter onLiveActivityResult: $message");
      },
      onRegisterPushToStartTokenResult: (Map<String, dynamic> message) async {
        LogUtil.d("flutter onRegisterPushToStartTokenResult: $message");
      },
      onReceiveOnlineState: (String online) async {
        LogUtil.d("flutter onReceiveOnlineState: $online");
      },
    );
  }
}
