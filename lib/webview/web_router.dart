import 'package:dlyz_flutter/webview/webview_page.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';

class WebRouter {
  static void jumpToWebPage(
    BuildContext context,
    String url,
    String title,
    Map<String, dynamic> params,
  ) {
    
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => WebViewPage(title: title, url: url),
      ),
    );
  }
}
