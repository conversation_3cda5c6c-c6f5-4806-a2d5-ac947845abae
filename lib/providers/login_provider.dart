import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:fluttertoast/fluttertoast.dart';
import '../model/login_info.dart';
import '../services/login_api_service.dart';
import '../net/example/mock_api.dart';
import '../model/user.dart';
import 'user_provider.dart';
import '../manager/channel_manager.dart';
import '../services/fast_login_http_util.dart';
import '../services/app_route_manager.dart';

/// 登录流程状态管理
class LoginStateProvider extends ChangeNotifier {
  LoginState _currentState = LoginState.initial;
  String _currentPhoneNumber = '';
  String _currentNonce = '';
  bool _isLoading = false;
  int _resendCountdown = 0;
  bool _fastTriedOnce = false;
  bool _isFastLoginInProgress = false;
  // 登录流程历史栈，用于统一返回逻辑
  final List<LoginState> _history = <LoginState>[];

  // Getters
  LoginState get currentState => _currentState;


  String get currentPhoneNumber => _currentPhoneNumber;

  String get currentNonce => _currentNonce;

  bool get isLoading => _isLoading;

  int get resendCountdown => _resendCountdown;
  bool get isFastLoginInProgress => _isFastLoginInProgress;

  /// Initialize the provider (alias for initializeLoginFlow)
  Future<void> initialize([BuildContext? context]) async {
    if (context != null) {
      await initializeLoginFlow(context);
    }
  }

  Future<void> initializeLoginFlow(BuildContext context) async {
    _setState(LoginState.loading);
    try {
      // 初始化用户数据，但默认展示手机号登录
      final userProvider = Provider.of<UserProvider>(context, listen: false);
      await userProvider.initialize();
      // 需求：优先显示手机登录，重置历史栈
      go(LoginState.phoneLogin, clear: true);
      // 后台尝试闪验（仅Android且会话内仅尝试一次）
      // 不阻塞UI，失败自动回落
      // ignore: discarded_futures
      tryFastLogin(context);
    } catch (e) {
      _showErrorToast('初始化失败: ${e.toString()}');
    }
  }

  // 统一导航入口封装
  void switchToAccountLogin() => go(LoginState.accountLogin);

  void switchToPhoneLogin() => go(LoginState.phoneLogin);

  void switchToPhonePasswordLogin() => go(LoginState.phonePasswordLogin);

  void switchToVerifyCode(String phoneNumber) {
    _currentPhoneNumber = phoneNumber;
    sendVerificationCode(phoneNumber);
  }

  /// 导航到下一个登录状态
  /// replace: 替换当前状态（不新增历史）
  /// clear: 清空历史后再入栈
  void go(
    LoginState next, {
    bool replace = false,
    bool clear = false,
  }) {
    if (clear) {
      _history.clear();
    }
    if (replace && _history.isNotEmpty) {
      _history.removeLast();
    }
    if (_history.isEmpty || _history.last != next) {
      _history.add(next);
    }
    _setState(next);
  }

  /// 返回到上一个登录状态。返回true表示已消费返回事件
  bool back() {
    if (_history.isEmpty) return false;
    // 弹出当前
    _history.removeLast();
    // 退回到上一状态；若栈空，维持初始并交给外层处理
    if (_history.isEmpty) {
      return false;
    }
    final previous = _history.last;
    _setState(previous);
    return true;
  }

  Future<bool> performAccountLogin({required BuildContext context, required String account, required String password}) async {
    _setLoading(true);
    try {
      final api = LoginApiService();
      final resp = await api.loginWithPassword(username: account, password: password);
      if (resp.success && resp.data != null) {
        final userProvider = Provider.of<UserProvider>(context, listen: false);
        final user = User.fromLoginInfo(resp.data!);
        await userProvider.loginSuccess(user: user);
        return true;
      } else {
        _showErrorToast(resp.message);
        return false;
      }
    } catch (e) {
      _showErrorToast('登录失败: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> performMobilePasswordLogin({required BuildContext context, required String mobile, required String password}) async {
    _setLoading(true);
    try {
      final api = LoginApiService();
      final resp = await api.loginWithMobilePassword(mobile: mobile, password: password);
      if (resp.success && resp.data != null) {
        final userProvider = Provider.of<UserProvider>(context, listen: false);
        final user = User.fromLoginInfo(resp.data!);
        await userProvider.loginSuccess(user: user);
        return true;
      } else {
        _showErrorToast(resp.message);
        return false;
      }
    } catch (e) {
      _showErrorToast('登录失败: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> sendVerificationCode(String phoneNumber) async {
    _setLoading(true);
    try {
      final api = LoginApiService();
      final resp = await api.sendCode(phone: phoneNumber);
      
      if (resp.success && resp.data != null) {
        _currentPhoneNumber = phoneNumber;
        // 保存nonce用于后续验证码校验
        _currentNonce = resp.data!.nonce;
        _startResendCountdown();
        return true;
      } else {
        _showErrorToast(resp.message);
        return false;
      }
    } catch (e) {
      _showErrorToast('发送验证码失败: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }


  Future<bool> verifyPhoneAndLogin({required String phoneNumber, required String verificationCode, BuildContext? context}) async {
    _setLoading(true);
    try {
      // 首先验证验证码
      final api = LoginApiService();
      final checkResult = await api.checkCode(
        phone: phoneNumber,
        code: verificationCode,
        nonce: _currentNonce,
      );
      
      if (checkResult.success && checkResult.data != null) {
        // 验证码正确，使用票据登录
        final loginResult = await api.loginWithMobileTicket(
          ticket: checkResult.data!.ticket,
          nonce: _currentNonce,
        );
        
        if (loginResult.success && loginResult.data != null) {
          if (context != null) {
            final userProvider = Provider.of<UserProvider>(context, listen: false);
            final user = User.fromLoginInfo(loginResult.data!);
            await userProvider.loginSuccess(user: user);
          }
          return true;
        } else {
          _showErrorToast(loginResult.message);
          return false;
        }
      } else {
        _showErrorToast(checkResult.message);
        return false;
      }
    } catch (e) {
      _showErrorToast('登录失败: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> performWechatLogin({required BuildContext context}) async {
    _setState(LoginState.wechatLogin);
    try {
      final result = await MockApiService().wechatLogin();
      if (result['success'] == true) {
        final user = User.fromLoginInfo(LoginInfo(loginType: 'wechat'));
        final userProvider = Provider.of<UserProvider>(context, listen: false);
        await userProvider.loginSuccess(user: user);
        // 直接返回true，不设置LoginState.success
        return true;
      } else {
        _showErrorToast(result['message'] ?? '微信登录失败');
        return false;
      }
    } catch (e) {
      _showErrorToast('微信登录失败: ${e.toString()}');
      return false;
    }
  }

  Future<void> resendVerificationCode() async {
    if (_resendCountdown > 0) return;
    await sendVerificationCode(_currentPhoneNumber);
  }

  void resetLoginFlow() {
    _currentState = LoginState.initial;
    _currentPhoneNumber = '';
    _currentNonce = '';
    _isLoading = false;
    _resendCountdown = 0;
    notifyListeners();
  }

  // state helpers
  void _setState(LoginState newState) {
    _currentState = newState;
    notifyListeners();
  }


  void _showErrorToast(String message) {
    _isLoading = false;
    Fluttertoast.showToast(
      msg: message,
      toastLength: Toast.LENGTH_LONG,
      gravity: ToastGravity.CENTER,
      backgroundColor: Colors.red,
      textColor: Colors.white,
    );
    notifyListeners();
  }


  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _startResendCountdown() {
    _resendCountdown = 60;
    _countdownTimer();
  }

  void _countdownTimer() {
    if (_resendCountdown > 0) {
      Future.delayed(const Duration(seconds: 1), () {
        _resendCountdown--;
        notifyListeners();
        _countdownTimer();
      });
    }
  }

  /// 尝试原生闪验登录（仅Android）。
  /// 不阻塞UI，内部自行处理回退与成功导航。
  Future<void> tryFastLogin(BuildContext context) async {
    // if (!Platform.isAndroid) {
    //   return;
    // }
    if (_fastTriedOnce || _isFastLoginInProgress) {
      return;
    }

    _isFastLoginInProgress = true;
    try {
      // 拉取配置（失败直接回落手机号登录，不进入错误重试页）
      Map<String, dynamic> config;
      try {
        config = await FastLoginHttpUtil.requestFastConfig();
      } catch (e) {
        go(LoginState.phoneLogin, replace: true);
        return;
      }

      // 设置协议地址（可选：按产品实际替换）
      final bool configOk = await ChannelManager().setFastLoginConfig(
        config,
        userAgreementUrl: 'https://www.baidu.com',
        privacyPolicyUrl: 'https://www.baidu.com',
      );
      if (!configOk) {
        go(LoginState.phoneLogin, replace: true);
        return;
      }

      // 环境检测
      final bool envOk = await ChannelManager().checkFastLoginEnvironment();
      if (!envOk) {
        go(LoginState.phoneLogin, replace: true);
        return;
      }

      if (Platform.isAndroid){ 
        // 初始化SDK
        final bool initOk = await ChannelManager().initializeFastLogin();
        if (!initOk) {
          go(LoginState.phoneLogin, replace: true);
          return;
        }

      }
      
      // 监听原生UI事件
      ChannelManager().setFastLoginUIEventListener((String event) {
        switch (event) {
          case 'close_clicked':
          case 'back_clicked':
            switchToPhoneLogin();
            break;
          case 'wechat_clicked':
            // 跳转微信登录流程
            go(LoginState.wechatLogin, replace: true);
            // ignore: discarded_futures
            performWechatLogin(context: context).then((success) {
              if (!success) {
                // 失败回落
                switchToPhoneLogin();
              }
            });
            break;
          case 'account_login_clicked':
            switchToAccountLogin();
            break;
          case 'other_phone_clicked':
            switchToPhoneLogin();
            break;
          case 'show_protocol_click_tip':
            break;
          default:
            // 未知事件：不处理
            break;
        }
      });

      // 不切换至 fastLogin，保持当前手机号登录界面，避免Loading视觉

      // 拉起并等待结果
      final Map<String, dynamic> result = await ChannelManager().doFastLogin();
      final success = result['success'] == true;
      final token = result['token'] as String?;

      if (!success || token == null || token.isEmpty) {
        _showErrorToast('闪验登录失败');
        return;
      }

      // 后端换票据
      final api = LoginApiService();
      final resp = await api.loginWithFastToken(token: token);
      if (resp.success && resp.data != null) {
        final userProvider = Provider.of<UserProvider>(context, listen: false);
        final user = User.fromLoginInfo(resp.data!);
        await userProvider.loginSuccess(user: user);
        if (context.mounted) {
          AppRouteManager.navigateAfterLogin(context);
        }
        return;
      }

      _showErrorToast(resp.message);
    } on FastLoginCancelException {
      // 用户主动取消 → 回落手机号
      switchToPhoneLogin();
    } catch (e) {
      // _setError('闪验异常: ${e.toString()}');
      switchToPhoneLogin();
    } finally {
      _isFastLoginInProgress = false;
      _fastTriedOnce = true;
      ChannelManager().setFastLoginUIEventListener(null);
    }
  }
}

enum LoginState {
  initial,
  loading,
  accountLogin,
  phoneLogin,
  phonePasswordLogin,
  wechatLogin,
  fastLogin,
  success,
  error,
}
