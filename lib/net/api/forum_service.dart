import '../http_service.dart';
import '../http_base_response.dart';
import '../sign_interceptor.dart';
import '../transformers/response_transformers.dart';
import '../../model/forum_category.dart';
import '../../model/forum_post_list.dart';
import '../../model/forum_config.dart';
import '../../model/topic_list.dart';
import '../../model/forum_login_info.dart';

class ForumService {
  static final ForumService _instance = ForumService._internal();
  factory ForumService() => _instance;
  ForumService._internal();
  static String authorization = "";

  final HttpService _httpService = HttpService();
  
  // 论坛统一headers
  static Map<String, dynamic> _forumHeaders = {
    'authorization': authorization,
    'forum-tgid': '147',
    'accept': '*/*',
    'Forum-Client': 'gamehub',
    'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
    'cache-control': 'no-cache',
    'content-type': 'application/json',
    'pragma': 'no-cache',
    'user-agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1',
  };

  /// 获取内容分类
  Future<BaseResponse<List<ForumCategory>>> getCategories({
    required String baseUrl,
  }) async {
    return await _httpService.get<List<ForumCategory>>(
      '/categories',
      baseUrl: baseUrl,
      headers: _forumHeaders,
      signType: SignType.v3,
      parseStrategy: ResponseTransformers.codeData<List<ForumCategory>>(),
      fromJsonT: (data) {
        // 直接解析 Data 字段中的数组
        if (data is List) {
          return data
              .map((item) => ForumCategory.fromJson(item))
              .toList();
        }
        return <ForumCategory>[];
      },
    );
  }

  /// 获取帖子列表
  Future<BaseResponse<ForumPostList>> getPostList({
    required String baseUrl,
    int? categoryId,
    String? categoryName,
    int page = 1,
    int pageSize = 5,
    int? scope,
    String? filterSearch,
  }) async {
    // 根据分类名称设置scope参数：发现分类使用scope=1，其他分类使用scope=0
    final int finalScope = scope ?? ((categoryName == '发现') ? 1 : 0);
    
    final queryParams = <String, dynamic>{
      'page': page,
      'perPage': pageSize,
      'scope': finalScope,
      'filter[attention]': 0,
      'filter[sort]': 1,
    };
    
    if (categoryId != null) {
      queryParams['filter[categoryids][0]'] = categoryId;
    }

    if (filterSearch != null && filterSearch.isNotEmpty) {
      queryParams['filter[search]'] = filterSearch;
    }

    return await _httpService.get<ForumPostList>(
      '/thread.list',
      baseUrl: baseUrl,
      queryParameters: queryParams,
      headers: _forumHeaders,
      signType: SignType.v3,
      parseStrategy: ResponseTransformers.codeData<ForumPostList>(),
      fromJsonT: (data) {
        // 直接解析 Data 字段中的对象
        return ForumPostList.fromJson(data);
      },
    );
  }

  /// 获取帖子详情
  Future<BaseResponse<ForumPost>> getPostDetail({
    required String baseUrl,
    required int threadId,
  }) async {
    return await _httpService.get<ForumPost>(
      '/thread.detail',
      baseUrl: baseUrl,
      queryParameters: {'threadId': threadId},
      headers: _forumHeaders,
      signType: SignType.v3,
      parseStrategy: ResponseTransformers.codeData<ForumPost>(),
      fromJsonT: (data) {
        // 直接解析 Data 字段中的对象
        return ForumPost.fromJson(data);
      },
    );
  }

  /// 获取论坛配置信息
  Future<BaseResponse<ForumConfig>> getForumConfig({
    required String baseUrl,
  }) async {
    return await _httpService.get<ForumConfig>(
      '/forum',
      baseUrl: baseUrl,
      headers: _forumHeaders,
      signType: SignType.v3,
      parseStrategy: ResponseTransformers.codeData<ForumConfig>(),
      fromJsonT: (data) {
        // 直接解析 Data 字段中的对象
        return ForumConfig.fromJson(data);
      },
    );
  }

  /// 获取分类对应的推荐/活动/话题配置
  /// 返回 Data 为数组，每个元素包含：style_name/title/content/count 等
  Future<BaseResponse<List<Map<String, dynamic>>>> getActLabel({
    required String baseUrl,
    int? categoryId,
  }) async {
    final queryParams = <String, dynamic>{
      'category_id': categoryId,
    };

    return await _httpService.get<List<Map<String, dynamic>>>(
      '/actlabelpage/query',
      baseUrl: baseUrl,
      queryParameters: queryParams,
      headers: _forumHeaders,
      signType: SignType.v3,
      parseStrategy: ResponseTransformers.codeData<List<Map<String, dynamic>>>(),
      fromJsonT: (data) {
        if (data is List) {
          return data.map<Map<String, dynamic>>((e) => Map<String, dynamic>.from(e as Map)).toList();
        }
        return <Map<String, dynamic>>[];
      },
    );
  }

  /// 举报帖子
  /// 对应接口: POST reports
  /// 请求体示例见 `lib/api2`，返回体示例见 `lib/api`
  Future<BaseResponse<Map<String, dynamic>>> reportThread({
    required String baseUrl,
    required int threadId,
    required String reason,
    required int userId,
    int type = 1,
  }) async {
    final Map<String, dynamic> body = {
      'reason': reason,
      'userId': userId,
      'type': type,
      'threadId': threadId,
    };

    return await _httpService.post<Map<String, dynamic>>(
      '/reports',
      baseUrl: baseUrl,
      data: body,
      signType: SignType.v3,
      headers: _forumHeaders,
      parseStrategy: ResponseTransformers.codeData<Map<String, dynamic>>(successCode: 0),
      fromJsonT: (data) {
        // 返回 Data 字段对象，例如 {"id": 3061}
        if (data is Map<String, dynamic>) {
          return Map<String, dynamic>.from(data);
        }
        return <String, dynamic>{};
      },
    );
  }

  /// 获取话题列表
  Future<BaseResponse<TopicList>> getTopicList({
    required String baseUrl,
    required int topicId,
    int hot = 0,
    int page = 1,
    int perPage = 10,
  }) async {
    final queryParams = <String, dynamic>{
      'filter[topicId]': topicId,
      'filter[hot]': hot,
      'perPage': perPage,
      'page': page,
    };

    return await _httpService.get<TopicList>(
      '/topics.list',
      baseUrl: baseUrl,
      queryParameters: queryParams,
      headers: _forumHeaders,
      signType: SignType.v3,
      parseStrategy: ResponseTransformers.codeData<TopicList>(),
      fromJsonT: (data) {
        // 直接解析 Data 字段中的对象
        return TopicList.fromJson(data);
      },
    );
  }

  /// 论坛登录接口
  /// 接口路径: ptbind
  /// 使用v3签名
  Future<BaseResponse<ForumLoginInfo>> forumLogin({
    required String baseUrl,
    required Map<String, dynamic> loginData,
  }) async {
    return await _httpService.post<ForumLoginInfo>(
      '/ptbind',
      baseUrl: baseUrl,
      data: loginData,
      headers: _forumHeaders,
      signType: SignType.v3,
      parseStrategy: ResponseTransformers.codeData<ForumLoginInfo>(),
      fromJsonT: (data) {
        // 直接解析 Data 字段中的对象
        return ForumLoginInfo.fromJson(data);
      },
    );
  }

  /// 更新授权信息
  /// 将tokenType和accessToken组合成authorization
  static void updateAuthorization(String? tokenType, String? accessToken) {
    if (tokenType != null && accessToken != null) {
      authorization = '$tokenType $accessToken';
      // 更新headers中的authorization
      _forumHeaders['authorization'] = authorization;
    }
  }
}

