import 'package:dlyz_flutter/manager/game_data_manager.dart';
import 'package:dlyz_flutter/model/game_post_list.dart';
import '../../model/mini_program_info.dart';
import '../common/common_params_v2.dart';
import '../http_service.dart';
import '../http_base_response.dart';
import '../sign_interceptor.dart';
import '../transformers/response_transformers.dart';

class GameListService {
  static final GameListService _instance = GameListService._internal();
  factory GameListService() => _instance;
  GameListService._internal();

  /// 获取游戏列表
  Future<BaseResponse<GamePostList>> getGameList({
    String baseUrl = 'http://gamehub-api.37.com.cn',
  }) async {
    try {
      // 组装通用参数（隐私协议请求通常在激活/登录前，因此 beforeActive=true）
      Map<String, dynamic> payload = {};
      payload.addAll(CommonParamsV2().transform());

      payload['keyword'] = '';
      payload['page_size'] = '20';
      payload['offset'] = '0';
      payload['package_name_list'] = GameDataManager().installedPackageNames;

      // 使用 HttpService 发送请求，指定 V3 签名
      final response = await HttpService.getInstance().post<GamePostList>(
        '/api/gamehub-api/v1/game-center/get-game-list',
        baseUrl: baseUrl,
        data: payload,
        contentType: ContentType.json,
        signType: SignType.v3,
        fromJsonT: (data) => GamePostList.fromJson(Map<String, dynamic>.from(data)),
        parseStrategy: ResponseTransformers.stateData<GamePostList>(),
      );

      return response;
    } catch (e) {
      return BaseResponse.error('未知错误: $e', code: -1);
    }
  }

  /// 获取跳转游戏配置
  Future<BaseResponse<MiniProgramInfo>> getJumpMiniProgramConfig({
    String baseUrl = 'http://sdk-api.37.com.cn',
    required int flagId,
  }) async {
    try {
      // 组装通用参数（隐私协议请求通常在激活/登录前，因此 beforeActive=true）
      Map<String, dynamic> payload = {};
      payload.addAll(CommonParamsV2().transform());

      payload['flag_id'] = flagId;

      // 使用 HttpService 发送请求，指定 V3 签名
      final response = await HttpService.getInstance().get<MiniProgramInfo>(
        '/api/mapi-service/v1/sdk/skip-applet-info',
        baseUrl: baseUrl,
        queryParameters: payload,
        contentType: ContentType.json,
        signType: SignType.v3,
        fromJsonT: (data) => MiniProgramInfo.fromJson(Map<String, dynamic>.from(data)),
        parseStrategy: ResponseTransformers.stateData<MiniProgramInfo>(),
      );

      return response;
    } catch (e) {
      return BaseResponse.error('未知错误: $e', code: -1);
    }
  }
}

