
/// 游戏中心游戏配置数据模型
class GameJumpConfig {
  final String wechatMinigramId;
  // 是否开启微信小游戏跳转,1为是，2为否
  final int isJumpWechatMinigram;
  final DownloadPageConfig downloadPageConfig;
  final List<PackageList> packageList;

  GameJumpConfig({
    required this.wechatMinigramId,
    required this.isJumpWechatMinigram,
    required this.downloadPageConfig,
    required this.packageList,
  });

  factory GameJumpConfig.fromJson(Map<String, dynamic> json) {
    return GameJumpConfig(
      wechatMinigramId: json['wechat_minigram_id'] ?? '',
      isJumpWechatMinigram: json['is_jump_wechat_minigram'] ?? 2,
      downloadPageConfig: DownloadPageConfig.fromJson(Map<String, dynamic>.from(json['download_page_config'] ?? {})),
      packageList: (json['package_list'] as List<dynamic>? ?? [])
          .map((item) => PackageList.fromJson(item))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'wechatMinigramId': wechatMinigramId,
      'isJumpWechatMinigram': isJumpWechatMinigram,
      'downloadPageConfig': downloadPageConfig.toJson(),
      'packageList': packageList.map((post) => post.toJson()).toList(),
    };
  }
}


/// 游戏中心详情数据模型
class DownloadPageConfig {
  final String isShowDownloadPage;
  final String officialPackageUrl;
  final String packageName;

  DownloadPageConfig({
    required this.isShowDownloadPage,
    required this.officialPackageUrl,
    required this.packageName,
  });

  factory DownloadPageConfig.fromJson(Map<String, dynamic> json) {
    return DownloadPageConfig(
      isShowDownloadPage: json['is_show_download_page'] ?? '',
      officialPackageUrl: json['official_package_url'] ?? '',
      packageName: json['package_name'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'isShowDownloadPage': isShowDownloadPage,
      'officialPackageUrl': officialPackageUrl,
      'packageName': packageName,
    };
  }
}

/// 悬浮信息配置
class PackageList {
  final String iconUrl;
  final String packageName;
  final String showMsg;

  PackageList({
    required this.iconUrl,
    required this.packageName,
    required this.showMsg
  });

  factory PackageList.fromJson(Map<String, dynamic> json) {
    return PackageList(
        iconUrl: json['icon_url'] ?? '',
        packageName: json['package_name'] ?? '',
        showMsg: json['show_msg'] ?? ''
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'iconUrl': iconUrl,
      'packageName': packageName,
      'showMsg': showMsg,
    };
  }
}