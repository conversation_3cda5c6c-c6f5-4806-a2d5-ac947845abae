/// 帖子列表数据模型
class ForumPostList {
  final List<ForumPost> pageData;
  final int currentPage;
  final int perPage;
  final String firstPageUrl;
  final String? nextPageUrl;
  final String prePageUrl;
  final int pageLength;
  final int totalCount;
  final int totalPage;

  ForumPostList({
    required this.pageData,
    required this.currentPage,
    required this.perPage,
    required this.firstPageUrl,
    this.nextPageUrl,
    required this.prePageUrl,
    required this.pageLength,
    required this.totalCount,
    required this.totalPage,
  });

  factory ForumPostList.fromJson(Map<String, dynamic> json) {
    return ForumPostList(
      pageData: (json['pageData'] as List<dynamic>? ?? [])
          .map((item) => ForumPost.fromJson(item))
          .toList(),
      currentPage: json['currentPage'] ?? 1,
      perPage: json['perPage'] ?? 20,
      firstPageUrl: json['firstPageUrl'] ?? '',
      nextPageUrl: json['nextPageUrl'],
      prePageUrl: json['prePageUrl'] ?? '',
      pageLength: json['pageLength'] ?? 20,
      totalCount: json['totalCount'] ?? 0,
      totalPage: json['totalPage'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'pageData': pageData.map((post) => post.toJson()).toList(),
      'currentPage': currentPage,
      'perPage': perPage,
      'firstPageUrl': firstPageUrl,
      'nextPageUrl': nextPageUrl,
      'prePageUrl': prePageUrl,
      'pageLength': pageLength,
      'totalCount': totalCount,
      'totalPage': totalPage,
    };
  }
}

/// 辅助方法：安全地将动态类型转换为布尔值
bool _parseBool(dynamic value) {
  if (value == null) return false;
  if (value is bool) return value;
  if (value is int) return value != 0;
  if (value is String) {
    return value.toLowerCase() == 'true' || value == '1';
  }
  return false;
}

/// 帖子数据模型
class ForumPost {
  final int threadId;
  final int postId;
  final int userId;
  final int categoryId;
  final int parentCategoryId;
  final int topicId;
  final String categoryName;
  final String parentCategoryName;
  final String title;
  final String displayTitle;
  final int viewCount;
  final int isApproved;
  final bool isStick;
  final bool isDraft;
  final bool isSite;
  final bool isAnonymous;
  final bool isFavorite;
  final double price;
  final double attachmentPrice;
  final int payType;
  final dynamic paid;
  final bool isLike;
  final bool isReward;
  final String createdAt;
  final String issueAt;
  final String updatedAt;
  final String diffTime;
  final ForumUser user;
  final dynamic group;
  final ForumLikeReward likeReward;
  final ForumDisplayTag displayTag;
  final ForumPosition position;
  final ForumAbility ability;
  final ForumContent content;
  final int freewords;
  final bool userStickStatus;
  final int favorCount;
  final String topics;
  final int reportStatus;
  final String startShowTime;
  final int isBottom;
  final String aiRank;
  final String aiType;
  final int pid;
  final int uid;
  final int gamePay;
  final String auditedBy;
  final String? auditedAt;
  final String updatedBy;
  final String createdBy;
  final String location;
  final int from;
  final int addAiContent;
  final int isRecommend;
  final dynamic voteId;
  final dynamic vote;
  final int isMixThread;

  ForumPost({
    required this.threadId,
    required this.postId,
    required this.userId,
    required this.categoryId,
    required this.parentCategoryId,
    required this.topicId,
    required this.categoryName,
    required this.parentCategoryName,
    required this.title,
    required this.displayTitle,
    required this.viewCount,
    required this.isApproved,
    required this.isStick,
    required this.isDraft,
    required this.isSite,
    required this.isAnonymous,
    required this.isFavorite,
    required this.price,
    required this.attachmentPrice,
    required this.payType,
    this.paid,
    required this.isLike,
    required this.isReward,
    required this.createdAt,
    required this.issueAt,
    required this.updatedAt,
    required this.diffTime,
    required this.user,
    this.group,
    required this.likeReward,
    required this.displayTag,
    required this.position,
    required this.ability,
    required this.content,
    required this.freewords,
    required this.userStickStatus,
    required this.favorCount,
    required this.topics,
    required this.reportStatus,
    required this.startShowTime,
    required this.isBottom,
    required this.aiRank,
    required this.aiType,
    required this.pid,
    required this.uid,
    required this.gamePay,
    required this.auditedBy,
    this.auditedAt,
    required this.updatedBy,
    required this.createdBy,
    required this.location,
    required this.from,
    required this.addAiContent,
    required this.isRecommend,
    this.voteId,
    this.vote,
    required this.isMixThread,
  });

  factory ForumPost.fromJson(Map<String, dynamic> json) {
    return ForumPost(
      threadId: json['threadId'] ?? 0,
      postId: json['postId'] ?? 0,
      userId: json['userId'] ?? 0,
      categoryId: json['categoryId'] ?? 0,
      parentCategoryId: json['parentCategoryId'] ?? 0,
      topicId: json['topicId'] ?? 0,
      categoryName: json['categoryName'] ?? '',
      parentCategoryName: json['parentCategoryName'] ?? '',
      title: json['title'] ?? '',
      displayTitle: json['display_title'] ?? '',
      viewCount: json['viewCount'] ?? 0,
      isApproved: json['isApproved'] ?? 0,
      isStick: _parseBool(json['isStick']),
      isDraft: _parseBool(json['isDraft']),
      isSite: _parseBool(json['isSite']),
      isAnonymous: _parseBool(json['isAnonymous']),
      isFavorite: _parseBool(json['isFavorite']),
      price: (json['price'] ?? 0).toDouble(),
      attachmentPrice: (json['attachmentPrice'] ?? 0).toDouble(),
      payType: json['payType'] ?? 0,
      paid: json['paid'],
      isLike: _parseBool(json['isLike']),
      isReward: _parseBool(json['isReward']),
      createdAt: json['createdAt'] ?? '',
      issueAt: json['issueAt'] ?? '',
      updatedAt: json['updatedAt'] ?? '',
      diffTime: json['diffTime'] ?? '',
      user: ForumUser.fromJson(json['user'] ?? {}),
      group: json['group'],
      likeReward: ForumLikeReward.fromJson(json['likeReward'] ?? {}),
      displayTag: ForumDisplayTag.fromJson(json['displayTag'] ?? {}),
      position: ForumPosition.fromJson(json['position'] ?? {}),
      ability: ForumAbility.fromJson(json['ability'] ?? {}),
      content: ForumContent.fromJson(json['content'] ?? {}),
      freewords: json['freewords'] ?? 0,
      userStickStatus: _parseBool(json['userStickStatus']),
      favorCount: json['favorCount'] ?? 0,
      topics: json['topics'] ?? '',
      reportStatus: json['report_status'] ?? 0,
      startShowTime: json['start_show_time'] ?? '',
      isBottom: json['is_bottom'] ?? 0,
      aiRank: json['ai_rank'] ?? '',
      aiType: json['ai_type'] ?? '',
      pid: json['pid'] ?? 0,
      uid: json['uid'] ?? 0,
      gamePay: json['game_pay'] ?? 0,
      auditedBy: json['audited_by'] ?? '',
      auditedAt: json['audited_at'],
      updatedBy: json['updated_by'] ?? '',
      createdBy: json['created_by'] ?? '',
      location: json['location'] ?? '',
      from: json['from'] ?? 0,
      addAiContent: json['add_ai_content'] ?? 0,
      isRecommend: json['is_recommend'] ?? 0,
      voteId: json['vote_id'],
      vote: json['vote'],
      isMixThread: json['is_mix_thread'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'threadId': threadId,
      'postId': postId,
      'userId': userId,
      'categoryId': categoryId,
      'parentCategoryId': parentCategoryId,
      'topicId': topicId,
      'categoryName': categoryName,
      'parentCategoryName': parentCategoryName,
      'title': title,
      'display_title': displayTitle,
      'viewCount': viewCount,
      'isApproved': isApproved,
      'isStick': isStick,
      'isDraft': isDraft,
      'isSite': isSite,
      'isAnonymous': isAnonymous,
      'isFavorite': isFavorite,
      'price': price,
      'attachmentPrice': attachmentPrice,
      'payType': payType,
      'paid': paid,
      'isLike': isLike,
      'isReward': isReward,
      'createdAt': createdAt,
      'issueAt': issueAt,
      'updatedAt': updatedAt,
      'diffTime': diffTime,
      'user': user.toJson(),
      'group': group,
      'likeReward': likeReward.toJson(),
      'displayTag': displayTag.toJson(),
      'position': position.toJson(),
      'ability': ability.toJson(),
      'content': content.toJson(),
      'freewords': freewords,
      'userStickStatus': userStickStatus,
      'favorCount': favorCount,
      'topics': topics,
      'report_status': reportStatus,
      'start_show_time': startShowTime,
      'is_bottom': isBottom,
      'ai_rank': aiRank,
      'ai_type': aiType,
      'pid': pid,
      'uid': uid,
      'game_pay': gamePay,
      'audited_by': auditedBy,
      'audited_at': auditedAt,
      'updated_by': updatedBy,
      'created_by': createdBy,
      'location': location,
      'from': from,
      'add_ai_content': addAiContent,
      'is_recommend': isRecommend,
      'vote_id': voteId,
      'vote': vote,
      'is_mix_thread': isMixThread,
    };
  }
}

/// 用户信息
class ForumUser {
  final int userId;
  final String nickname;
  final String avatar;
  final String badge;
  final String label;
  final String color;
  final dynamic medal;
  final int threadCount;
  final int followCount;
  final int fansCount;
  final int likedCount;
  final int questionCount;
  final bool isRealName;
  final String joinedAt;
  final int follow;

  ForumUser({
    required this.userId,
    required this.nickname,
    required this.avatar,
    required this.badge,
    required this.label,
    required this.color,
    this.medal,
    required this.threadCount,
    required this.followCount,
    required this.fansCount,
    required this.likedCount,
    required this.questionCount,
    required this.isRealName,
    required this.joinedAt,
    required this.follow,
  });

  factory ForumUser.fromJson(Map<String, dynamic> json) {
    return ForumUser(
      userId: json['userId'] ?? 0,
      nickname: json['nickname'] ?? '',
      avatar: json['avatar'] ?? '',
      badge: json['badge'] ?? '',
      label: json['label'] ?? '',
      color: json['color'] ?? '',
      medal: json['medal'],
      threadCount: json['threadCount'] ?? 0,
      followCount: json['followCount'] ?? 0,
      fansCount: json['fansCount'] ?? 0,
      likedCount: json['likedCount'] ?? 0,
      questionCount: json['questionCount'] ?? 0,
      isRealName: _parseBool(json['isRealName']),
      joinedAt: json['joinedAt'] ?? '',
      follow: json['follow'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'nickname': nickname,
      'avatar': avatar,
      'badge': badge,
      'label': label,
      'color': color,
      'medal': medal,
      'threadCount': threadCount,
      'followCount': followCount,
      'fansCount': fansCount,
      'likedCount': likedCount,
      'questionCount': questionCount,
      'isRealName': isRealName,
      'joinedAt': joinedAt,
      'follow': follow,
    };
  }
}

/// 点赞奖励信息
class ForumLikeReward {
  final List<dynamic> users;
  final int likePayCount;
  final int shareCount;
  final int postCount;

  ForumLikeReward({
    required this.users,
    required this.likePayCount,
    required this.shareCount,
    required this.postCount,
  });

  factory ForumLikeReward.fromJson(Map<String, dynamic> json) {
    return ForumLikeReward(
      users: json['users'] ?? [],
      likePayCount: json['likePayCount'] ?? 0,
      shareCount: json['shareCount'] ?? 0,
      postCount: json['postCount'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'users': users,
      'likePayCount': likePayCount,
      'shareCount': shareCount,
      'postCount': postCount,
    };
  }
}

/// 显示标签
class ForumDisplayTag {
  final bool isPoster;
  final bool isEssence;
  final dynamic isRedPack;
  final dynamic isReward;
  final bool isVote;

  ForumDisplayTag({
    required this.isPoster,
    required this.isEssence,
    this.isRedPack,
    this.isReward,
    required this.isVote,
  });

  factory ForumDisplayTag.fromJson(Map<String, dynamic> json) {
    return ForumDisplayTag(
      isPoster: _parseBool(json['isPoster']),
      isEssence: _parseBool(json['isEssence']),
      isRedPack: json['isRedPack'],
      isReward: json['isReward'],
      isVote: _parseBool(json['isVote']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'isPoster': isPoster,
      'isEssence': isEssence,
      'isRedPack': isRedPack,
      'isReward': isReward,
      'isVote': isVote,
    };
  }
}

/// 位置信息
class ForumPosition {
  final String longitude;
  final String latitude;
  final String address;
  final String location;

  ForumPosition({
    required this.longitude,
    required this.latitude,
    required this.address,
    required this.location,
  });

  factory ForumPosition.fromJson(Map<String, dynamic> json) {
    return ForumPosition(
      longitude: json['longitude'] ?? '',
      latitude: json['latitude'] ?? '',
      address: json['address'] ?? '',
      location: json['location'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'longitude': longitude,
      'latitude': latitude,
      'address': address,
      'location': location,
    };
  }
}

/// 权限信息
class ForumAbility {
  final bool canEdit;
  final bool canDelete;
  final bool canEssence;
  final bool canPoster;
  final bool canStick;
  final bool canReply;
  final bool canViewPost;
  final bool canFreeViewPost;
  final bool canViewVideo;
  final bool canViewAttachment;
  final bool canDownloadAttachment;

  ForumAbility({
    required this.canEdit,
    required this.canDelete,
    required this.canEssence,
    required this.canPoster,
    required this.canStick,
    required this.canReply,
    required this.canViewPost,
    required this.canFreeViewPost,
    required this.canViewVideo,
    required this.canViewAttachment,
    required this.canDownloadAttachment,
  });

  factory ForumAbility.fromJson(Map<String, dynamic> json) {
    return ForumAbility(
      canEdit: _parseBool(json['canEdit']),
      canDelete: _parseBool(json['canDelete']),
      canEssence: _parseBool(json['canEssence']),
      canPoster: _parseBool(json['canPoster']),
      canStick: _parseBool(json['canStick']),
      canReply: _parseBool(json['canReply']),
      canViewPost: _parseBool(json['canViewPost']),
      canFreeViewPost: _parseBool(json['canFreeViewPost']),
      canViewVideo: _parseBool(json['canViewVideo']),
      canViewAttachment: _parseBool(json['canViewAttachment']),
      canDownloadAttachment: _parseBool(json['canDownloadAttachment']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'canEdit': canEdit,
      'canDelete': canDelete,
      'canEssence': canEssence,
      'canPoster': canPoster,
      'canStick': canStick,
      'canReply': canReply,
      'canViewPost': canViewPost,
      'canFreeViewPost': canFreeViewPost,
      'canViewVideo': canViewVideo,
      'canViewAttachment': canViewAttachment,
      'canDownloadAttachment': canDownloadAttachment,
    };
  }
}

/// 内容信息
class ForumContent {
  final String text;
  final Map<String, dynamic> indexes;
  final dynamic poster;
  final List<dynamic> images;
  final List<dynamic> videos;
  final String pureText;

  ForumContent({
    required this.text,
    required this.indexes,
    this.poster,
    required this.images,
    required this.videos,
    required this.pureText,
  });

  factory ForumContent.fromJson(Map<String, dynamic> json) {
    final indexesData = json['indexes'];
    Map<String, dynamic> indexes = {};
    if (indexesData is Map<String, dynamic>) {
      indexes = indexesData;
    }
    
    return ForumContent(
      text: json['text'] ?? '',
      indexes: indexes,
      poster: json['poster'] != null ? Map<String, dynamic>.from(json['poster']) : null,
      images: json['images'] ?? [],
      videos: json['videos'] ?? [],
      pureText: json['pure_text'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'text': text,
      'indexes': indexes,
      'poster': poster,
      'images': images,
      'videos': videos,
      'pure_text': pureText,
    };
  }
}