
import 'package:dlyz_flutter/model/game_jump_config.dart';

/// 游戏中心数据模型
class GamePostList {
  final int offset;
  final List<GameListDetail> record;

  GamePostList({
    required this.offset,
    required this.record,
  });

  factory GamePostList.fromJson(Map<String, dynamic> json) {
    return GamePostList(
      offset: json['offset'] ?? 0,
      record: (json['record'] as List<dynamic>? ?? [])
          .map((item) => GameListDetail.fromJson(item))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'offset': offset,
      'record': record.map((post) => post.toJson()).toList(),
    };
  }
}


/// 游戏中心详情数据模型
class GameListDetail {
  final String tGid;
  final String tGidName;
  final String tag;
  final String slogan;
  final String banner;
  final String bannerPreview;
  final String detailPage;
  final String icon;
  final GameFloatConfig floatConfig;
  GameJumpConfig? gameJumpConfig;

  GameListDetail({
    required this.tGid,
    required this.tGidName,
    required this.tag,
    required this.slogan,
    required this.banner,
    required this.bannerPreview,
    required this.detailPage,
    required this.icon,
    required this.floatConfig,
    this.gameJumpConfig
  });

  factory GameListDetail.fromJson(Map<String, dynamic> json) {
    return GameListDetail(
      tGid: json['tgid'] ?? '',
      tGidName: json['tgid_name'] ?? '',
      tag: json['tag'] ?? '',
      slogan: json['slogan'] ?? '',
      banner: json['banner'] ?? '',
      bannerPreview: json['banner_preview'] ?? '',
      detailPage: json['detail_page'] ?? '',
      icon: json['icon'] ?? '',
      floatConfig: GameFloatConfig.fromJson(json['float_config'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'tGid': tGid,
      'tGidName': tGidName,
      'tag': tag,
      'slogan': slogan,
      'banner': banner,
      'bannerPreview': bannerPreview,
      'detailPage': detailPage,
      'icon': icon,
      'floatConfig': floatConfig,
    };
  }
}

/// 悬浮信息配置
class GameFloatConfig {
  final String floatMsg;

  GameFloatConfig({
    required this.floatMsg
  });

  factory GameFloatConfig.fromJson(Map<String, dynamic> json) {
    return GameFloatConfig(
        floatMsg: json['float_msg'] ?? ''
    );
  }

}