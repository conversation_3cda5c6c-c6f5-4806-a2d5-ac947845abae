/// 授权应用项数据模型
class AuthAppItem {
  /// 微信小程序ID
  final String wechatMinigramId;
  
  /// 图标URL
  final String icon;
  
  /// 应用名称
  final String name;

  const AuthAppItem({
    required this.wechatMinigramId,
    required this.icon,
    required this.name,
  });

  AuthAppItem copyWith({
    String? wechatMinigramId,
    String? icon,
    String? name,
  }) {
    return AuthAppItem(
      wechatMinigramId: wechatMinigramId ?? this.wechatMinigramId,
      icon: icon ?? this.icon,
      name: name ?? this.name,
    );
  }

  factory AuthAppItem.fromJson(Map<String, dynamic> json) {
    return AuthAppItem(
      wechatMinigramId: json['wechat_minigram_id']?.toString() ?? '',
      icon: json['icon_url']?.toString() ?? '',
      name: json['display_name']?.toString() ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'wechat_minigram_id': wechatMinigramId,
      'icon': icon,
      'name': name,
    };
  }
}

/// 授权应用列表响应数据模型
class AuthAppListData {
  /// 响应状态码
  final int state;
  
  /// 响应消息
  final String msg;
  
  /// 追踪ID
  final String traceId;
  
  /// 随机值
  final String nonce;
  
  /// 授权应用列表
  final List<AuthAppItem> list;

  const AuthAppListData({
    required this.state,
    required this.msg,
    required this.traceId,
    required this.nonce,
    required this.list,
  });

  AuthAppListData copyWith({
    int? state,
    String? msg,
    String? traceId,
    String? nonce,
    List<AuthAppItem>? list,
  }) {
    return AuthAppListData(
      state: state ?? this.state,
      msg: msg ?? this.msg,
      traceId: traceId ?? this.traceId,
      nonce: nonce ?? this.nonce,
      list: list ?? this.list,
    );
  }

  factory AuthAppListData.fromJson(Map<String, dynamic> json) {
    return AuthAppListData(
      state: json['state'] as int? ?? 0,
      msg: json['msg'] as String? ?? '',
      traceId: json['trace_id'] as String? ?? '',
      nonce: json['nonce'] as String? ?? '',
      list: (json['list'] as List<dynamic>?)
          ?.map((item) {
            if (item is Map<String, dynamic>) {
              return AuthAppItem.fromJson(item);
            } else if (item is Map) {
              // 处理 Map<dynamic, dynamic> 类型
              return AuthAppItem.fromJson(Map<String, dynamic>.from(item));
            } else {
              // 如果不是Map类型，返回空的AuthAppItem
              return const AuthAppItem(wechatMinigramId: '', icon: '', name: '');
            }
          })
          .toList() ?? [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'state': state,
      'msg': msg,
      'trace_id': traceId,
      'nonce': nonce,
      'list': list.map((item) => item.toJson()).toList(),
    };
  }
}