import 'package:flutter/material.dart';
import 'package:dlyz_flutter/common/dl_color.dart';

import 'cache_image.dart';

/// 游戏角色绑定底部弹窗
class GameCharacterBindingDialog extends StatefulWidget {
  final ValueChanged<GameCharacter>? onCharacterSelected;
  final VoidCallback? onBindOtherCharacter;
  final VoidCallback? onClose;

  const GameCharacterBindingDialog({
    super.key,
    this.onCharacterSelected,
    this.onBindOtherCharacter,
    this.onClose,
  });

  /// 显示游戏角色绑定弹窗
  static void show({
    required BuildContext context,
    ValueChanged<GameCharacter>? onCharacterSelected,
    VoidCallback? onBindOtherCharacter,
    VoidCallback? onClose,
  }) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => GameCharacterBindingDialog(
        onCharacterSelected: onCharacterSelected,
        onBindOtherCharacter: onBindOtherCharacter,
        onClose: onClose,
      ),
    );
  }

  @override
  State<GameCharacterBindingDialog> createState() => _GameCharacterBindingDialogState();
}

class _GameCharacterBindingDialogState extends State<GameCharacterBindingDialog> {
  int? selectedIndex;
  List<GameCharacter> characters = [];
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadCharacters();
  }

  /// 加载角色数据
  Future<void> _loadCharacters() async {
    // TODO: 这里后续会替换成接口获取
    // 模拟网络请求延迟
    await Future.delayed(const Duration(milliseconds: 500));

    // 模拟角色数据
    final mockCharacters = [
      const GameCharacter(
        id: '1',
        name: '角色昵称',
        server: '区服区服',
        level: '99级',
        avatar: 'https://imgcs.s98s2.com/common/1747279036火舞头像.png',
      ),
      const GameCharacter(
        id: '2',
        name: '角色昵称',
        server: '区服区服',
        level: '99级',
        avatar: 'https://imgcs.s98s2.com/common/1747279033胡列娜头像.png',
      ),
    ];

    if (mounted) {
      setState(() {
        characters = mockCharacters;
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildHeader(),
          if (isLoading)
            _buildLoadingState()
          else if (characters.isEmpty)
            _buildEmptyState()
          else
            _buildCharacterList(),
          _buildBindOtherButton(),
          SizedBox(height: MediaQuery.of(context).padding.bottom + 20),
        ],
      ),
    );
  }

  /// 构建头部
  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 20),
      child: Column(
        children: [
          // 顶部指示条
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: DLColor.divider,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: 16),
          // 标题
          const Text(
            '游戏角色绑定列表',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: DLColor.textPrimary,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建加载状态
  Widget _buildLoadingState() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 40),
      child: const Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(DLColor.primary),
        ),
      ),
    );
  }

  /// 构建空状态
  Widget _buildEmptyState() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 40),
      child: const Center(
        child: Text(
          '暂无绑定角色',
          style: TextStyle(
            fontSize: 16,
            color: DLColor.textThird,
          ),
        ),
      ),
    );
  }

  /// 构建角色列表
  Widget _buildCharacterList() {
    return Container(
      constraints: BoxConstraints(
        maxHeight: MediaQuery.of(context).size.height * 0.5,
      ),
      child: ListView.builder(
        shrinkWrap: true,
        padding: const EdgeInsets.symmetric(horizontal: 20),
        itemCount: characters.length,
        itemBuilder: (context, index) {
          return _buildCharacterItem(characters[index], index);
        },
      ),
    );
  }

  /// 构建单个角色项
  Widget _buildCharacterItem(GameCharacter character, int index) {
    final isSelected = selectedIndex == index;
    
    return GestureDetector(
      onTap: () {
        setState(() {
          selectedIndex = index;
        });
        widget.onCharacterSelected?.call(character);
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: 12),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? DLColor.primary : DLColor.border,
            width: isSelected ? 2 : 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.05),
              spreadRadius: 1,
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            // 角色头像
            ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: CachedImage(
                imageUrl: character.avatar,
                width: 48,
                height: 48,
                fit: BoxFit.cover,
              ),
            ),
            const SizedBox(width: 12),
            // 角色信息
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    character.name,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: DLColor.textPrimary,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${character.server}  ${character.level}',
                    style: const TextStyle(
                      fontSize: 14,
                      color: DLColor.textThird,
                    ),
                  ),
                ],
              ),
            ),
            // 选择状态
            Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: isSelected ? DLColor.primary : DLColor.border,
                  width: 2,
                ),
                color: isSelected ? DLColor.primary : Colors.transparent,
              ),
              child: isSelected
                  ? const Icon(
                      Icons.check,
                      color: Colors.white,
                      size: 16,
                    )
                  : null,
            ),
          ],
        ),
      ),
    );
  }

  /// 构建绑定其他角色按钮
  Widget _buildBindOtherButton() {
    return Container(
      margin: const EdgeInsets.all(20),
      child: GestureDetector(
        onTap: () {
          Navigator.pop(context);
          widget.onBindOtherCharacter?.call();
        },
        child: Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(vertical: 16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: DLColor.border,
              width: 1,
            ),
          ),
          child: const Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.add,
                color: DLColor.textSecondary,
                size: 20,
              ),
              SizedBox(width: 8),
              Text(
                '绑定其他角色',
                style: TextStyle(
                  fontSize: 16,
                  color: DLColor.textSecondary,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// 游戏角色数据模型
class GameCharacter {
  final String id;
  final String name;
  final String server;
  final String level;
  final String avatar;
  final bool isSelected;

  const GameCharacter({
    required this.id,
    required this.name,
    required this.server,
    required this.level,
    required this.avatar,
    this.isSelected = false,
  });

  GameCharacter copyWith({
    String? id,
    String? name,
    String? server,
    String? level,
    String? avatar,
    bool? isSelected,
  }) {
    return GameCharacter(
      id: id ?? this.id,
      name: name ?? this.name,
      server: server ?? this.server,
      level: level ?? this.level,
      avatar: avatar ?? this.avatar,
      isSelected: isSelected ?? this.isSelected,
    );
  }
}
