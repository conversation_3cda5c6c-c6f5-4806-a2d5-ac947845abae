
import 'dart:io';
import 'package:dlyz_flutter/components/select_game_bottom_dialog.dart';
import 'package:dlyz_flutter/components/video_player_card.dart';
import 'package:dlyz_flutter/model/game_card_info.dart';
import 'package:dlyz_flutter/services/download/internal/ALDownloaderStringExtension.dart';
import 'package:flutter/material.dart';
import '../common/dl_color.dart';
import '../model/download_info.dart';
import '../manager/channel_manager.dart';
import 'cache_image.dart';
import 'download_bottom_dialog.dart';

class GameCard extends StatefulWidget {
  final GameCardInfo game;
  final VoidCallback? onTap;
  final VoidCallback? onDownload;

  const GameCard({
    super.key,
    required this.game,
    this.onTap,
    this.onDownload,
  });

  @override
  State<GameCard> createState() => _GameCardState();
}

class _GameCardState extends State<GameCard> {

  @override
  void initState() {
    super.initState();
    _checkInstallation();
  }

  /// 检查是否已安装
  Future<void> _checkInstallation() async {
    if (widget.game.packageName.isEmpty) return;

    try {
      final isInstalled = await ChannelManager().checkGameInstalled(packageName: widget.game.packageName);
      if (mounted) {
        setState(() {
          widget.game.downloadInfo.downloadStatusNotifier.value = isInstalled ? "打开游戏" : widget.game.downloadInfo.downloadStatusNotifier.value;
        });
      }
    } catch (e) {
      print("检查游戏是否安装失败: $e");
    }
  }

  /// 打开游戏
  void _openGame() {
    if (Platform.isAndroid) {
      if (widget.game.channelPackages.isNotEmpty) {
        SelectGamePackageBottomDialog.show(
            context: context,
            packages: widget.game.channelPackages,
            onConfirm: (game) => {
              ChannelManager().openInstalledGame(packageName: game.packageName)
            }
        );
      } else if (widget.game.miniProgram.isNotEmpty) {
        DownloadBottomDialog.show(context: context, game: widget.game);
      } else {
        ChannelManager().openInstalledGame(packageName: widget.game.packageName);
      }
    } else if (Platform.isIOS) {
      if (widget.game.miniProgram.isEmpty) {
        ChannelManager().openInstalledGame(packageName: widget.game.packageName);
      } else {
        DownloadBottomDialog.show(context: context, game: widget.game);
      }
    }

  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4.0),
      child: GestureDetector(
        onTap: widget.onTap,
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white,
          ),
          child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 12.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 游戏信息和下载按钮
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 12.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(
                          children: [
                            ClipRRect(
                              child: ClipRRect(
                                borderRadius: const BorderRadius.all(Radius.circular(6.0)),
                                child: CachedImage(
                                  imageUrl: widget.game.iconUrl,
                                  height: 32,
                                  width: 32,
                                ),
                              ),
                            ),
                            SizedBox(
                              width: 6,
                            ),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  widget.game.title,
                                  style: const TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                Text(
                                  widget.game.type,
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.grey[600],
                                  ),
                                ),
                              ],
                            )
                          ],
                        ),
                        _buildDownloadBtn(
                            game: widget.game,
                            onDownload: widget.onDownload
                        ),
                      ],
                    ),
                  ),
                  Text(
                    widget.game.subtitle,
                    textAlign: TextAlign.left,
                    style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600
                    ),
                  ),
                  SizedBox(
                    height: 8,
                  ),
                  widget.game.videoUrl.isNotEmpty
                  ? ClipRRect(
                    borderRadius: const BorderRadius.all(Radius.circular(12.0)),
                    child: VideoPlayerCard(
                        videoUrl: widget.game.videoUrl,
                        coverUrl: widget.game.imageUrl,
                        height: 180,
                        autoPlay: false,
                    ),
                  ) : ClipRRect(
                    borderRadius: const BorderRadius.all(Radius.circular(12.0)),
                    child: CachedImage(
                      imageUrl: widget.game.imageUrl,
                      height: 180,
                    ),
                  ),
                  SizedBox(
                    height: 16,
                  ),
                ],
              ),
          ),
        )
      ),
    );
  }

  /// 打开游戏按钮
  Widget _openGameBtn(String btn, GameCardInfo game) {
    return Stack(
      alignment: Alignment.center,
      clipBehavior: Clip.none,
      children: [
        ElevatedButton(
          onPressed: () => _openGame(),
          style: ElevatedButton.styleFrom(
              backgroundColor: DLColor.primary,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(6),
              ),
              padding: EdgeInsets.zero,
              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
              side: BorderSide.none,
              minimumSize: Size(90, 30),
              maximumSize: Size(90, 30)
          ),
          child: Text(
            btn,
            style: TextStyle(
                color: Colors.white
            ),
          ),
        ),
        if (game.tag.isNotEmpty) ...[
          Positioned(
            top: -10,
            right: -10,
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 4, vertical: 1),
              decoration: BoxDecoration(
                color: Colors.red,
                borderRadius: BorderRadius.only(topLeft: Radius.circular(4.0), topRight: Radius.circular(4.0), bottomRight: Radius.circular(4.0)),
              ),
              child: Text(
                game.tag,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          )
        ]
      ],
    );
  }

  /// 下载按钮
  Widget _buildDownloadBtn({required GameCardInfo game, required void Function()? onDownload}) {
    return ValueListenableBuilder<String>(
        valueListenable: game.downloadInfo.downloadStatusNotifier,
        builder: (context, value, child) {
          if (value.isEqualTo("打开游戏")) {
            return _openGameBtn(value, game);
          } else if (value.isEqualTo("下载中")) {
            return ValueListenableBuilder<String>(
                valueListenable: game.downloadInfo.progressNotifier,
                builder: (context, progressValue, child) {
                  final progress = double.tryParse(progressValue.replaceAll('%', '')) ?? 0;

                  if (progress > 0) {
                    game.downloadInfo.cacheProgress = progress;
                  }

                  final displayProgress = progress > 0 ? progress : game.downloadInfo.cacheProgress ?? 0;
                  return Stack(
                    alignment: Alignment.center,
                    clipBehavior: Clip.none,
                    children: [
                      ElevatedButton(
                          onPressed: onDownload,
                          style: ElevatedButton.styleFrom(
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(6),
                              ),
                              padding: EdgeInsets.zero,
                              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                              side: BorderSide.none,
                              minimumSize: Size(90, 30),
                              maximumSize: Size(90, 30)
                          ),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(6),
                            child: Stack(
                              children: [
                                LinearProgressIndicator(
                                  value: displayProgress / 100,
                                  backgroundColor: DLColor.downloadBackground,
                                  valueColor: const AlwaysStoppedAnimation<Color>(DLColor.primary),
                                  minHeight: 30.0,
                                ),
                                Center(
                                  child: Text(
                                    progressValue,
                                    style: TextStyle(
                                      color: Colors.white,
                                    ),
                                  ),
                                )
                              ],
                            ),
                          )
                      ),
                      if (game.tag.isNotEmpty) ...[
                        Positioned(
                          top: -10,
                          right: -10,
                          child: Container(
                            padding: EdgeInsets.symmetric(horizontal: 4, vertical: 1),
                            decoration: BoxDecoration(
                              color: Colors.red,
                              borderRadius: BorderRadius.only(topLeft: Radius.circular(4.0), topRight: Radius.circular(4.0), bottomRight: Radius.circular(4.0)),
                            ),
                            child: Text(
                              game.tag,
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        )
                      ]
                    ],
                  );
                }
            );
          } else if (value.isEqualTo("继续")) {
            return Stack(
              alignment: Alignment.center,
              clipBehavior: Clip.none,
              children: [
                ElevatedButton(
                    onPressed: onDownload,
                    style: ElevatedButton.styleFrom(
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(6),
                        ),
                        padding: EdgeInsets.zero,
                        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                        side: BorderSide.none,
                        minimumSize: Size(90, 30),
                        maximumSize: Size(90, 30)
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(6),
                      child: Stack(
                        children: [
                          LinearProgressIndicator(
                            value: (game.downloadInfo.cacheProgress ?? 0.0) / 100,
                            backgroundColor: DLColor.downloadBackground,
                            valueColor: const AlwaysStoppedAnimation<Color>(DLColor.primary),
                            minHeight: 30.0,
                          ),
                          Center(
                            child: Text(
                              value,
                              style: TextStyle(
                                color: Colors.white,
                              ),
                            ),
                          )
                        ],
                      ),
                    )
                ),
                if (game.tag.isNotEmpty) ...[
                  Positioned(
                    top: -10,
                    right: -10,
                    child: Container(
                      padding: EdgeInsets.symmetric(horizontal: 4, vertical: 1),
                      decoration: BoxDecoration(
                        color: Colors.red,
                        borderRadius: BorderRadius.only(topLeft: Radius.circular(4.0), topRight: Radius.circular(4.0), bottomRight: Radius.circular(4.0)),
                      ),
                      child: Text(
                        game.tag,
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  )
                ]
              ],
            );
          } else {
            return Stack(
              alignment: Alignment.center,
              clipBehavior: Clip.none,
              children: [
                ElevatedButton(
                    onPressed: onDownload,
                    style: ElevatedButton.styleFrom(
                        backgroundColor: value.isEqualTo("安装") ? DLColor.tagBackground : DLColor.primary,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(6),
                        ),
                        padding: EdgeInsets.zero,
                        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                        side: BorderSide.none,
                        minimumSize: Size(90, 30),
                        maximumSize: Size(90, 30)
                    ),
                    child: Text(
                      value,
                      style: TextStyle(
                        color: Colors.white,
                      ),
                    )
                ),
                if (game.tag.isNotEmpty) ...[
                  Positioned(
                    top: -10,
                    right: -10,
                    child: Container(
                      padding: EdgeInsets.symmetric(horizontal: 4, vertical: 1),
                      decoration: BoxDecoration(
                        color: Colors.red,
                        borderRadius: BorderRadius.only(topLeft: Radius.circular(4.0), topRight: Radius.circular(4.0), bottomRight: Radius.circular(4.0)),
                      ),
                      child: Text(
                        game.tag,
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  )
                ]
              ],
            );
          }
        }
    );
  }
}