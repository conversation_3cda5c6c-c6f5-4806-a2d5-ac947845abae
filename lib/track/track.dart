import 'package:thinking_analytics/td_analytics.dart';

class SqTrackManager {
  static final _taAppId = 'a64b5a07f57d4bee80ef2658d897789d';
  static final _taServerUrl = 'https://ta.shan-yu-tech.com';

  static bool _isInit = false;

  //单例生命
  factory SqTrackManager() => _instance;

  SqTrackManager._internal();

  static final SqTrackManager _instance = SqTrackManager._internal();

  /// 隐私授权之后调用
  static Future<void> init() async {
    if (_isInit) {
      return;
    }
    _isInit = true;
    //SDK初始化
    await TDAnalytics.init(_taAppId, _taServerUrl);
    TDAnalytics.enableLog(true);
    TDAnalytics.setSuperProperties({});
    TDAnalytics.enableAutoTrack(TDAutoTrackEventType.APP_START |
    TDAutoTrackEventType.APP_END |
    TDAutoTrackEventType.APP_INSTALL |
    TDAutoTrackEventType.APP_CRASH);
    // 设置动态公共属性, 动态公共属性不支持自动采集事件
    TDAnalytics.setDynamicSuperProperties(() {
      return _getDynamicSuperProperties();
    });
  }

  ///动态公共属性
  static Map<String, dynamic> _getDynamicSuperProperties() {
    return <String, dynamic>{'DYNAMIC_DATE': DateTime.now().toUtc()};
  }

  static SqTrackManager getInstance() {
    return _instance;
  }

  ///设置账号ID
  void setUserId(String userId) {
    TDAnalytics.login(userId);
  }

  Future<void> track(String eventName, [Map<String, dynamic>? properties]) async {
    TDAnalytics.track(eventName, properties: properties);
  }
}
