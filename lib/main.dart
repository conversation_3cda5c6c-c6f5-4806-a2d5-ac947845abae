import 'dart:async';
import 'dart:convert';
import 'dart:ui';
import 'package:dlyz_flutter/common/dl_color.dart';
import 'package:dlyz_flutter/manager/network_manager.dart';
import 'package:dlyz_flutter/net/http_service.dart';
import 'package:dlyz_flutter/net/api/forum_service.dart';
import 'package:dlyz_flutter/model/forum_login_info.dart';
import 'package:dlyz_flutter/net/config/http_base_config.dart';
import 'package:dlyz_flutter/pages/community/CommunityPage.dart';
import 'package:dlyz_flutter/pages/demo/DemoPage.dart';
import 'package:dlyz_flutter/pages/games/GamesPage.dart';
import 'package:dlyz_flutter/pages/profile/ProfilePage.dart';
import 'package:dlyz_flutter/pages/splash/SplashPage.dart';
import 'package:dlyz_flutter/pages/privacy/privacy_check_page.dart';
import 'package:dlyz_flutter/pages/demo/proxy_config_page.dart';
import 'package:dlyz_flutter/push/sq_push_manager.dart';
import 'package:flutter/foundation.dart';
import 'package:dlyz_flutter/services/download/ALDownloader.dart';
import 'package:dlyz_flutter/track/track.dart';
import 'package:dlyz_flutter/utils/log_util.dart';
import 'package:dlyz_flutter/utils/sp_utils.dart';
import 'package:dlyz_flutter/config/app_config.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:provider/provider.dart';
import 'bugless/app_error_handler.dart';
import 'info/forum_info.dart';
import 'providers/login_provider.dart';
import 'providers/user_provider.dart';
import 'providers/download_provider.dart';
import 'providers/app_review_provider.dart';
import 'providers/game_circle_provider.dart';
import 'pages/games/GameCirclePage.dart';

final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

void main() async {
  AppErrorHandler.run(
    const MyApp(),
    initFunc: () async {
      await initApp();
    },
  );
}

///初始化
Future<void> initApp() async {
  await SqTrackManager.init();
  await SpManager.init();

  // 初始化应用配置
  await AppConfig.initialize();

  ALDownloader.initialize();
  ALDownloader.configurePrint(true, frequentEnabled: false);

  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  SqPushManager().init();
  
  // 在应用初始化时就设置NavigatorKey
  HttpService.getInstance().setNavigatorKey(navigatorKey);
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> with WidgetsBindingObserver {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    // 销毁网络监听
    NetworkManager().dispose();
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    // 当应用暂停或退出时暂停所有下载任务
    if (state == AppLifecycleState.paused ||
        state == AppLifecycleState.detached) {
      ALDownloader.pauseAll();
    }
  }

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => LoginStateProvider()),
        ChangeNotifierProvider(create: (_) => UserProvider()),
        ChangeNotifierProvider(create: (_) => DownloadProvider()),
        ChangeNotifierProvider(create: (_) => AppReviewProvider()),
        ChangeNotifierProvider(create: (_) => GameCircleProvider()),
      ],
      child: MaterialApp(
        navigatorKey: navigatorKey,
        theme: ThemeData(
          // 全局背景色
          scaffoldBackgroundColor: Colors.white,
        ),
        initialRoute: '/',
        routes: {
          '/': (context) => const InitialPageResolver(),
          '/privacy-check': (context) => const PrivacyCheckPage(),
          '/splash': (context) => const SplashPage(),
          '/game-circle': (context) => const GameCirclePage(),
          '/home': (context) => const MyHomePage(),
        },
        onGenerateRoute: (RouteSettings settings) {
          // 这里可以根据需要添加其他动态路由
          return null;
        },
      ),
    );
  }
}

class MyHomePage extends StatefulWidget {
  const MyHomePage({super.key});

  @override
  State<MyHomePage> createState() => _MyHomePageState();
}

class _MyHomePageState extends State<MyHomePage> {
  int _currentIndex = 0;
  DateTime? _lastBackPressTime;
  bool _isLoadingForumConfig = true;

  final List<Widget> _pages = const [
    CommunityPage(),
    GamesPage(),
    ProfilePage(),
    DemoPage(title: 'Demo'),
  ];

  @override
  void initState() {
    super.initState();
    _initializeLoginProvider();
    _initializeForumConfig();
    _initializeHttpService();
  }

  Future<void> _initializeHttpService() async {
    try {
      final userProvider = Provider.of<UserProvider>(context, listen: false);
      await userProvider.initialize();
      HttpService.getInstance().setUserProvider(userProvider);
      HttpService.getInstance().setNavigatorKey(navigatorKey);
    } catch (e) {
      print('HttpService UserProvider 初始化失败: $e');
    }
  }

  Future<void> _initializeLoginProvider() async {
    try {
      final loginProvider = Provider.of<LoginStateProvider>(
        context,
        listen: false,
      );
      await loginProvider.initialize();
    } catch (e) {
      print('LoginProvider 初始化失败: $e');
    }
  }

  Future<void> _initializeForumConfig() async {
    var ticket = context.read<UserProvider>().currentTicket;
    try {
      // 1. 先调用论坛登录接口
      final forumService = ForumService();
      // 准备登录数据（您需要根据实际需求修改这些参数）
      final loginData = {
        'app_ticket': ticket
      };

      final loginResponse = await forumService.forumLogin(
        baseUrl: HttpBaseConfig.forumBaseUrl,
        loginData: loginData,
      );

      if (loginResponse.code == 0 && loginResponse.data != null) {
        // 2. 将返回的tokenType和accessToken设置到ForumService的authorization
        final loginInfo = loginResponse.data!;
        ForumService.updateAuthorization(
          loginInfo.tokenType,
          loginInfo.accessToken,
        );

        print('论坛登录成功，已设置authorization: ${ForumService.authorization}');

        // 3. 请求论坛配置接口
        final forumInfo = ForumInfo();
        final configResult = await forumInfo.initialize();

        if (configResult) {
          print('论坛配置初始化成功');
        } else {
          print('论坛配置初始化失败');
        }
      } else {
        print('论坛登录失败: ${loginResponse.message}');
      }
    } catch (e) {
      print('论坛配置初始化失败: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoadingForumConfig = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoadingForumConfig) {
      return const Scaffold(body: Center(child: CircularProgressIndicator()));
    }

    return PopScope(
      onPopInvokedWithResult: _onWillPop,
      child: Scaffold(
        // 移除AppBar
        body: SafeArea(
          child: IndexedStack(index: _currentIndex, children: _pages),
        ),
        // 底部导航栏
        bottomNavigationBar: BottomNavigationBar(
          type: BottomNavigationBarType.fixed,
          currentIndex: _currentIndex,
          onTap: (index) {
            setState(() {
              _currentIndex = index;
            });
          },
          items: [
            BottomNavigationBarItem(
              icon: SvgPicture.asset('assets/images/home_icon.svg', width: 18, height: 18),
              activeIcon: SvgPicture.asset('assets/images/home_action_icon.svg', width: 18, height: 18),
              label: '圈子',
            ),
            BottomNavigationBarItem(
              icon: SvgPicture.asset('assets/images/games_icon.svg', width: 18, height: 18),
              activeIcon: SvgPicture.asset('assets/images/games_action_icon.svg', width: 18, height: 18),
              label: '游戏',
            ),
            BottomNavigationBarItem(
              icon: SvgPicture.asset('assets/images/profile_icon.svg', width: 18, height: 18),
              activeIcon: SvgPicture.asset('assets/images/profile_action_icon.svg', width: 18, height: 18),
              label: '我的',
            ),
            BottomNavigationBarItem(
              icon: SizedBox.shrink(),
              label: 'Demo',
            ),
          ],
          // 设置选中颜色
          selectedItemColor: DLColor.primary,
          // 设置未选中颜色
          unselectedItemColor: DLColor.textThird,
          // 显示选中和未选中的标签
          showSelectedLabels: true,
          showUnselectedLabels: true,
          // 移除底部导航栏自带的背景色
          backgroundColor: Colors.transparent,
          elevation: 0,
        ),

      ),
    );
  }

  void _onWillPop(bool didPop, dynamic result) async {
    if (!didPop) {
      final now = DateTime.now();

      if (_lastBackPressTime == null ||
          now.difference(_lastBackPressTime!).inMilliseconds > 2000) {
        // 第一次按返回键或超过2秒
        _lastBackPressTime = now;

        // 显示提示
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('再按一次退出应用'),
            duration: Duration(seconds: 2),
            backgroundColor: Colors.black87,
          ),
        );
      } else {
        // 第二次按返回键，退出应用
        Navigator.of(context).pop();
      }
    }
  }
}

/// 初始页面解析器
/// 根据代理配置文件决定启动时显示哪个页面
class InitialPageResolver extends StatefulWidget {
  const InitialPageResolver({super.key});

  @override
  State<InitialPageResolver> createState() => _InitialPageResolverState();
}

class _InitialPageResolverState extends State<InitialPageResolver> {
  Widget? _targetPage;

  @override
  void initState() {
    super.initState();
    _resolveInitialPage();
  }

  /// 解析初始页面
  Future<void> _resolveInitialPage() async {
    Widget targetPage;

    if (kDebugMode) {
      // Debug模式下检查配置文件
      bool shouldShowProxyConfig = await _shouldShowProxyConfig();
      targetPage =
          shouldShowProxyConfig
              ? const ProxyConfigPage()
              : const PrivacyCheckPage();
    } else {
      // Release模式直接跳转隐私检查页面
      targetPage = const PrivacyCheckPage();
    }

    if (mounted) {
      setState(() {
        _targetPage = targetPage;
      });
    }
  }

  /// 检查是否应该显示代理配置页面
  Future<bool> _shouldShowProxyConfig() async {
    try {
      final String jsonString = await rootBundle.loadString(
        'assets/config/proxy_config.json',
      );
      final Map<String, dynamic> config = json.decode(jsonString);
      return config['isShow'] == true;
    } catch (e) {
      print('读取代理配置文件失败: $e');
      // 读取失败时默认不显示代理配置页面
      return false;
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_targetPage == null) {
      // 配置加载中，显示加载界面
      return const Scaffold(
        backgroundColor: Colors.white,
        body: Center(child: CircularProgressIndicator()),
      );
    }

    return _targetPage!;
  }
}
