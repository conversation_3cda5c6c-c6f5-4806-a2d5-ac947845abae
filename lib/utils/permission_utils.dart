import 'dart:io';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:installed_apps/installed_apps.dart';
import 'package:installed_apps/app_info.dart';

import '../common/dl_color.dart';

/// 权限类型枚举
enum PermissionType {
  camera,
  microphone,
  storage,
  location,
  photos,
  notification,
}

class PermissionUtils {

  /// 权限说明文案
  static const Map<PermissionType, String> _permissionDescriptions = {
    PermissionType.camera: '需要相机权限来拍摄照片和录制视频',
    PermissionType.microphone: '需要麦克风权限来录制音频',
    PermissionType.storage: '需要存储权限来保存文件',
    PermissionType.location: '需要位置权限来获取您的位置信息',
    PermissionType.photos: '需要访问相册权限用于选择和保存照片',
    PermissionType.notification: '需要通知权限权限用于推送信息',
  };

  // 本地标记Key
  static const Map<PermissionType, String> _deniedKeys = {
    PermissionType.camera: 'camera_permission_denied',
    PermissionType.microphone: 'microphone_permission_denied',
    PermissionType.storage: 'storage_permission_denied',
    PermissionType.location: 'location_permission_denied',
    PermissionType.photos: 'photos_permission_denied',
    PermissionType.notification: 'notification_permission_denied',
  };

  static Future<void> _setPermissionDenied(PermissionType type, bool denied) async {
    final prefs = await SharedPreferences.getInstance();
    final key = _deniedKeys[type];
    if (key != null) {
      await prefs.setBool(key, denied);
    }
  }

  static Future<bool> _getPermissionDenied(PermissionType type) async {
    final prefs = await SharedPreferences.getInstance();
    final key = _deniedKeys[type];
    if (key != null) {
      return prefs.getBool(key) ?? false;
    }
    return false;
  }

  /// 获取权限对象
  static Permission _getPermission(PermissionType type) {
    switch (type) {
      case PermissionType.camera:
        return Permission.camera;
      case PermissionType.microphone:
        return Permission.microphone;
      case PermissionType.storage:
        return Permission.storage;
      case PermissionType.location:
        return Permission.location;
      case PermissionType.photos:
        return Permission.photos;
      case PermissionType.notification:
        return Permission.notification;
    }
  }


  /// 显示顶部权限说明弹窗（无按钮，与系统弹窗同时存在）
  static VoidCallback _showTopPermissionExplanation(
    BuildContext context,
    PermissionType type,
    String description,
  ) {
    OverlayEntry? overlayEntry;
    
    overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        top: MediaQuery.of(context).padding.top + 20,
        left: 20,
        right: 20,
        child: Material(
          color: Colors.transparent,
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      _getPermissionIcon(type),
                      color: _getPermissionColor(type),
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      _getPermissionTitle(type),
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  description,
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 8),
                const Text(
                  '请在接下来的弹窗中允许权限',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );

    Overlay.of(context).insert(overlayEntry);
    
    // 返回移除弹窗的函数
    return () {
      overlayEntry?.remove();
    };
  }

  static Future<bool> _showPermissionExplanationDialog(
    BuildContext context,
    PermissionType type,
    String description,
  ) async {
    return await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(_getPermissionTitle(type)),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(description),
              const SizedBox(height: 16),
              const Text(
                '请在接下来的弹窗中允许权限，以便正常使用该功能。',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey,
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('取消'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('确定'),
            ),
          ],
        );
      },
    ) ?? false;
  }

  /// 显示设置引导弹窗
  static Future<bool> _showSettingsDialog(
    BuildContext context,
    PermissionType type,
    String description,
  ) async {
    return await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(_getPermissionTitle(type)),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(description),
              const SizedBox(height: 16),
              const Text(
                '权限已被拒绝，请在系统设置中手动开启权限。',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.red,
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('取消'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop(true);
                openAppSettings();
              },
              child: const Text('去设置'),
            ),
          ],
        );
      },
    ) ?? false;
  }

  /// 获取权限标题
  static String _getPermissionTitle(PermissionType type) {
    switch (type) {
      case PermissionType.camera:
        return '相机权限';
      case PermissionType.microphone:
        return '麦克风权限';
      case PermissionType.storage:
        return '存储权限';
      case PermissionType.location:
        return '位置权限';
      case PermissionType.photos:
        return '相册权限';
      case PermissionType.notification:
        return '通知权限';
    }
  }

  /// 检查权限状态
  static Future<bool> isPermissionGranted(PermissionType type) async {
    final permission = _getPermission(type);
    return await permission.isGranted;
  }

  /// 检查权限是否被永久拒绝
  static Future<bool> isPermissionPermanentlyDenied(PermissionType type) async {
    final permission = _getPermission(type);
    return await permission.isPermanentlyDenied;
  }

  /// 通用权限申请方法
  static Future<bool> requestPermission(
    BuildContext context,
    PermissionType type, {
    String? description,
  }) async {
    final denied = await _getPermissionDenied(type);
    final desc = description ?? _permissionDescriptions[type] ?? '';
    if (denied) {
      // 直接弹去设置弹窗
      await _showSettingsDialog(context, type, desc);
      return false;
    }
    final permission = _getPermission(type);
    final status = await permission.status;
    if (status.isGranted) {
      await _setPermissionDenied(type, false);
      return true;
    }
    if (status.isPermanentlyDenied) {
      await _setPermissionDenied(type, true);
      await _showSettingsDialog(context, type, desc);
      return false;
    }
    // 第一次 denied，显示顶部说明弹窗，然后请求权限
    if (status.isDenied) {
      // 显示顶部说明弹窗，并获取移除函数
      final removeOverlay = _showTopPermissionExplanation(context, type, desc);
      // 延迟一下再请求权限，让用户看到说明
      await Future.delayed(const Duration(milliseconds: 500));
      final result = await permission.request();
      // 移除顶部弹窗
      removeOverlay();
      if (result.isGranted) {
        await _setPermissionDenied(type, false);
        return true;
      } else {
        await _setPermissionDenied(type, true);
        await _showSettingsDialog(context, type, desc);
        return false;
      }
    }
    // 其他情况
    return false;
  }

  /// 申请相机权限（便捷方法）
  static Future<bool> requestCameraPermission(BuildContext context, {String? description}) {
    return requestPermission(context, PermissionType.camera, description: description);
  }

  /// 便捷方法：请求麦克风权限
  static Future<bool> requestMicrophonePermission(BuildContext context, {String? description}) {
    return requestPermission(context, PermissionType.microphone, description: description);
  }

  /// 便捷方法：请求存储权限
  static Future<bool> requestStoragePermission(BuildContext context, {String? description}) {
    return requestPermission(context, PermissionType.storage, description: description);
  }

  /// 便捷方法：请求位置权限
  static Future<bool> requestLocationPermission(BuildContext context, {String? description}) {
    return requestPermission(context, PermissionType.location, description: description);
  }

  /// 便捷方法：请求相册权限
  static Future<bool> requestPhotosPermission(BuildContext context, {String? description}) async {
    if (Platform.isIOS) {
      // iOS用photos权限
      return requestPermission(context, PermissionType.photos, description: description);
    } else if (Platform.isAndroid) {
      final deviceInfo = DeviceInfoPlugin();
      final androidInfo = await deviceInfo.androidInfo;
      if (androidInfo.version.sdkInt >= 33) {
        // Android 13+ 用photos权限
        return requestPermission(context, PermissionType.photos, description: description);
      } else {
        // Android 13以下用storage权限
        return requestPermission(context, PermissionType.storage, description: description);
      }
    } else {
      // 其他平台直接返回true
      return Future.value(true);
    }
  }

  /// 便捷方法：请求通知权限
  static Future<bool> requestNotificationPermission(BuildContext context, {String? description}) {
    return requestPermission(context, PermissionType.notification, description: description);
  }


  /// 获取权限图标
  static IconData _getPermissionIcon(PermissionType type) {
    switch (type) {
      case PermissionType.camera:
        return Icons.camera_alt;
      case PermissionType.microphone:
        return Icons.mic;
      case PermissionType.storage:
        return Icons.folder;
      case PermissionType.location:
        return Icons.location_on;
      case PermissionType.photos:
        return Icons.photo_library;
      case PermissionType.notification:
        return Icons.notifications;
    }
  }

  /// 获取权限颜色
  static Color _getPermissionColor(PermissionType type) {
    switch (type) {
      case PermissionType.camera:
        return Colors.blue;
      case PermissionType.microphone:
        return Colors.green;
      case PermissionType.storage:
        return DLColor.primary;
      case PermissionType.location:
        return Colors.red;
      case PermissionType.photos:
        return Colors.purple;
      case PermissionType.notification:
        return Colors.teal;
    }
  }

  /// 获取已安装应用列表（使用 installed_apps 插件，Android Only）
  static Future<List<Map<String, String>>> getInstalledApps() async {
    List<AppInfo> apps = await InstalledApps.getInstalledApps(true, true);
    return apps
        .map((app) => {
              'appName': app.name ?? '',
              'packageName': app.packageName ?? '',
            })
        .toList();
  }
}