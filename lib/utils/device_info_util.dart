import 'dart:io';
import 'dart:math';
import 'package:device_info_plus/device_info_plus.dart';

import 'package:package_info_plus/package_info_plus.dart';

import '../manager/channel_manager.dart';
import '../model/connectivity_result.dart';
import 'md5_utils.dart';
import 'sp_utils.dart';

/// 设备信息工具类
class DeviceInfoUtil {
  static DeviceInfoPlugin? _deviceInfoPlugin;
  static PackageInfo? _packageInfo;
  
  /// 获取设备信息
  static Future<Map<String, dynamic>> getDeviceInfo() async {
    _deviceInfoPlugin ??= DeviceInfoPlugin();
    _packageInfo ??= await PackageInfo.fromPlatform();
    
    final Map<String, dynamic> deviceInfo = {};
    
    if (Platform.isAndroid) {
      deviceInfo.addAll({
        'os': 'android',
        'dev': 'a4bc1a02bb27a18870806fff77ebe5d6', //todo 后续生成
        'oaid': '1231231231', //todo 后续生成
      });
    } else if (Platform.isIOS) {
      final iosInfo = await _deviceInfoPlugin!.iosInfo;
      deviceInfo.addAll({
        'os': 'ios',
        'dev': '${iosInfo.name}_${iosInfo.model}',
        'caid': '', // iOS 广告ID，需要通过专门的插件获取
      });
    }
    
    return deviceInfo;
  }

  /// 解析网络状态
  static List<ConnectivityResult> parseConnectivityResults(List<String> states) {
    return states.map((state) {
      switch (state.trim()) {
        case 'bluetooth':
          return ConnectivityResult.bluetooth;
        case 'wifi':
          return ConnectivityResult.wifi;
        case 'ethernet':
          return ConnectivityResult.ethernet;
        case 'mobile':
          return ConnectivityResult.mobile;
        case 'vpn':
          return ConnectivityResult.vpn;
        case 'other':
          return ConnectivityResult.other;
        default:
          return ConnectivityResult.none;
      }
    }).toList();
  }

  /// 安卓生成 dev：md5(imei + mac + androidId + 时间戳)
  static Future<String> getDev() async {
    const String cacheKey = 'cached_dev';

    // 先尝试从本地缓存获取
    String? cachedDev = SpManager.getInstance().getString(cacheKey);
    if (cachedDev != null && cachedDev.isNotEmpty) {
      return cachedDev;
    }

    String imei = getImei(); //不再获取imei
    String mac = getMacAddress(); //不再获取mac
    String? androidId = '';
    String dev = '';

    if (Platform.isAndroid) {
      androidId = await getAndroidID();
      final String timestamp = DateTime.now().millisecondsSinceEpoch.toString();
      final String raw = '$imei$mac$androidId$timestamp';
      dev =  Md5Utils.generateMd5(raw);
    } else if (Platform.isIOS) {
      String idfa = (await getIDFA()) ?? '';
      String idfv = (await getIDFV()) ?? '';
      String temp = "-:&d4@zXqm-pLgW";
      dev = Md5Utils.generateMd5('$idfa$idfv$temp');
    }

    // 保存到本地缓存
    if (dev.isNotEmpty) {
      SpManager.getInstance().put(cacheKey, dev);
    }

    return dev;
  }

  static Future<String> getAndroidID() async {
    if (!Platform.isAndroid) {
      return "";
    }

    const String cacheKey = 'cached_android_id';

    // 先尝试从本地缓存获取
    String? cachedAndroidId = SpManager.getInstance().getString(cacheKey);
    if (cachedAndroidId != null && cachedAndroidId.isNotEmpty) {
      return cachedAndroidId;
    }

    // 如果本地没有，则从原生获取
    String androidId = await ChannelManager().getAndroidId() ?? '';

    // 保存到本地缓存
    if (androidId.isNotEmpty) {
      SpManager.getInstance().put(cacheKey, androidId);
    }

    return androidId;
  }

  /// 获取MAC地址
  static String getMacAddress() {
    if (!Platform.isAndroid) {
      return "";
    }
    // 由于隐私限制，返回固定的MAC地址
    return "020000000000";
  }

  /// 获取IMEI
  static String getImei() {
    // 由于隐私限制，返回随机的IMEI
    if (!Platform.isAndroid) {
      return "";
    }
    const String cacheKey = 'cached_imei';
    const int IMEI_RANDOM_LENGTH = 14; // 随机生成imei, 随机数的长度
    const String IMEI_PREFIX = "999"; // 根据实际情况修改前缀

    // 先尝试从本地缓存获取
    String? cachedImei = SpManager.getInstance().getString(cacheKey);
    if (cachedImei != null && cachedImei.isNotEmpty) {
      return cachedImei;
    }

    // 使用当前毫秒时间戳作为随机数种子，对应Java的System.currentTimeMillis()
    final random = Random(DateTime.now().millisecondsSinceEpoch);

    // 构建14位随机数字
    final StringBuffer randomNum = StringBuffer();
    for (int i = 0; i < IMEI_RANDOM_LENGTH; i++) {
      // 生成0-8的随机整数（nextInt(9)表示范围是0到8，共9个数字）
      int subNum = random.nextInt(9);
      randomNum.write(subNum);
    }
    // 拼接前缀和随机数部分
    String imei = "$IMEI_PREFIX${randomNum.toString()}";

    // 保存到本地缓存
    if (imei.isNotEmpty) {
      SpManager.getInstance().put(cacheKey, imei);
    }

    return imei;
  }

  // ios获取IDFA (广告标识符)
  static Future<String?> getIDFA() async {
    if (!Platform.isIOS) {
      return "";
    }
    const String cacheKey = 'cached_idfa';

    // 先尝试从本地缓存获取
    String? cachedIdfa = SpManager.getInstance().getString(cacheKey);
    if (cachedIdfa != null && cachedIdfa.isNotEmpty) {
      return cachedIdfa;
    }
    
    // 通过平台通道获取IDFA (使用ASIdentifierManager)
    try {
      String? idfa = await ChannelManager().getIDFA();
      
      // 保存到本地缓存
      if (idfa != null && idfa.isNotEmpty) {
        SpManager.getInstance().put(cacheKey, idfa);
      }
      
      return idfa ?? "";
    } catch (e) {
      print('获取IDFA失败: $e');
      return "";
    }
  }

  // ios获取IDFV (供应商标识符)
  static Future<String?> getIDFV() async {
    if (!Platform.isIOS) {
      return "";
    }
    const String cacheKey = 'cached_idfv';

    // 先尝试从本地缓存获取
    String? cachedIdfv = SpManager.getInstance().getString(cacheKey);
    if (cachedIdfv != null && cachedIdfv.isNotEmpty) {
      return cachedIdfv;
    }

    DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
    try {
      IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
      String? idfv = iosInfo.identifierForVendor; // 注意：IDFA需要用户授权

      // 保存到本地缓存
      if (idfv != null && idfv.isNotEmpty) {
        SpManager.getInstance().put(cacheKey, idfv);
      }
      return idfv;
    } catch (e) {
      print('获取IDFV失败: $e');
      return "";
    }
  }

  // ios请求广告追踪权限
  static Future<String> requestTrackingPermission() async {
    if (!Platform.isIOS) {
      return "not_supported";
    }
    
    try {
      String status = await ChannelManager().requestTrackingPermission();
      return status;
    } catch (e) {
      print('请求广告追踪权限失败: $e');
      return "error";
    }
  }


}